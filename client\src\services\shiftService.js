/**
 * Shift Management Service
 * Handles all shift-related API operations
 */

import { getApiBaseUrl } from '../utils/network-utils';

class ShiftService {
  constructor() {
    this.baseUrl = getApiBaseUrl();
  }

  /**
   * Get authentication headers
   */
  getAuthHeaders() {
    const token = localStorage.getItem('hauling_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  /**
   * Get all shifts with optional filtering
   */
  async getShifts(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const url = `${this.baseUrl}/shifts${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch shifts: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching shifts:', error);
      throw error;
    }
  }

  /**
   * Get current active shift for a truck
   */
  async getCurrentShift(truckId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/current/${truckId}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (response.status === 404) {
        return { success: true, data: null }; // No active shift
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch current shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current shift:', error);
      throw error;
    }
  }

  /**
   * Create a new shift
   */
  async createShift(shiftData) {
    try {
      console.log('SHIFT_SERVICE_CREATE', 'Creating shift', {
        shift_data: shiftData,
        mode: shiftData.mode,
        is_bulk: shiftData.mode === 'range',
        date_range: shiftData.mode === 'range' ? `${shiftData.start_date} to ${shiftData.end_date}` : shiftData.shift_date
      });

      const response = await fetch(`${this.baseUrl}/shifts`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(shiftData)
      });

      console.log('SHIFT_SERVICE_CREATE_RESPONSE', 'Create response received', {
        status: response.status,
        ok: response.ok,
        status_text: response.statusText
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('SHIFT_SERVICE_CREATE_ERROR', 'Create failed', {
          status: response.status,
          error_data: errorData,
          request_data: shiftData
        });
        throw new Error(errorData.message || `Failed to create shift: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('SHIFT_SERVICE_CREATE_SUCCESS', 'Create successful', {
        result: result,
        created_count: Array.isArray(result.data) ? result.data.length : 1
      });

      return result;
    } catch (error) {
      console.error('SHIFT_SERVICE_CREATE_EXCEPTION', 'Create exception', {
        error: error.message,
        request_data: shiftData
      });
      throw error;
    }
  }

  /**
   * Update an existing shift
   */
  async updateShift(shiftId, updateData) {
    try {
      console.log('SHIFT_SERVICE_UPDATE', 'Updating shift', {
        shift_id: shiftId,
        update_data: updateData,
        is_status_only: Object.keys(updateData).length === 1 && updateData.status
      });

      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(updateData)
      });

      console.log('SHIFT_SERVICE_UPDATE_RESPONSE', 'Update response received', {
        shift_id: shiftId,
        status: response.status,
        ok: response.ok
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('SHIFT_SERVICE_UPDATE_ERROR', 'Update failed', {
          shift_id: shiftId,
          status: response.status,
          error_data: errorData,
          request_data: updateData
        });
        throw new Error(errorData.message || `Failed to update shift: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('SHIFT_SERVICE_UPDATE_SUCCESS', 'Update successful', {
        shift_id: shiftId,
        result: result
      });

      return result;
    } catch (error) {
      console.error('SHIFT_SERVICE_UPDATE_EXCEPTION', 'Update exception', {
        shift_id: shiftId,
        error: error.message,
        update_data: updateData
      });
      throw error;
    }
  }

  /**
   * Cancel a shift (soft delete - changes status to cancelled)
   */
  async cancelShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/${shiftId}/cancel`, {
        method: 'PATCH',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to cancel shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error cancelling shift:', error);
      throw error;
    }
  }

  /**
   * Delete a shift permanently (enhanced with related shifts option)
   */
  async deleteShift(shiftId, deleteRelated = false) {
    try {
      const url = deleteRelated
        ? `${this.baseUrl}/shifts/${shiftId}?delete_related=true`
        : `${this.baseUrl}/shifts/${shiftId}`;

      const response = await fetch(url, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting shift:', error);
      throw error;
    }
  }

  /**
   * Delete entire shift group (all related shifts created together)
   */
  async deleteShiftGroup(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/group/${shiftId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete shift group: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting shift group:', error);
      throw error;
    }
  }

  /**
   * Create a shift handover
   */
  async createHandover(handoverData) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/handover`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(handoverData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create handover: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating handover:', error);
      throw error;
    }
  }

  /**
   * Get shift handovers with optional filtering
   */
  async getHandovers(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
          params.append(key, filters[key]);
        }
      });

      const url = `${this.baseUrl}/shifts/handovers${params.toString() ? `?${params.toString()}` : ''}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch handovers: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching handovers:', error);
      throw error;
    }
  }

  /**
   * Manually activate a shift
   */
  async activateShift(shiftId) {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/activate/${shiftId}`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to activate shift: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error activating shift:', error);
      throw error;
    }
  }

  /**
   * Get shifts for a specific date range
   */
  async getShiftsByDateRange(startDate, endDate, filters = {}) {
    return this.getShifts({
      ...filters,
      start_date: startDate,
      end_date: endDate
    });
  }

  /**
   * Auto-activate shifts based on current date/time
   */
  async autoActivateShifts() {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/auto-activate`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to auto-activate shifts: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error auto-activating shifts:', error);
      throw error;
    }
  }

  /**
   * Schedule automatic activation (for periodic calls)
   */
  async scheduleAutoActivation() {
    try {
      const response = await fetch(`${this.baseUrl}/shifts/schedule-auto-activation`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to schedule auto-activation: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error scheduling auto-activation:', error);
      throw error;
    }
  }

  /**
   * Get shifts for today
   */
  async getTodayShifts(filters = {}) {
    const today = new Date().toISOString().split('T')[0];
    return this.getShifts({
      ...filters,
      shift_date: today
    });
  }

  /**
   * Get shifts for a specific truck
   */
  async getTruckShifts(truckId, filters = {}) {
    return this.getShifts({
      ...filters,
      truck_id: truckId
    });
  }

  /**
   * Get shifts for a specific driver
   */
  async getDriverShifts(driverId, filters = {}) {
    return this.getShifts({
      ...filters,
      driver_id: driverId
    });
  }

  /**
   * Get active shifts
   */
  async getActiveShifts(filters = {}) {
    return this.getShifts({
      ...filters,
      status: 'active'
    });
  }

  /**
   * Get scheduled shifts
   */
  async getScheduledShifts(filters = {}) {
    return this.getShifts({
      ...filters,
      status: 'scheduled'
    });
  }

  /**
   * Utility function to format shift time for display
   */
  formatShiftTime(timeString) {
    if (!timeString) return '';
    
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    
    return `${displayHour}:${minutes} ${ampm}`;
  }

  /**
   * Utility function to get shift type display name
   */
  getShiftTypeDisplay(shiftType) {
    const types = {
      'day': 'Day Shift',
      'night': 'Night Shift',
      'custom': 'Custom Shift'
    };
    
    return types[shiftType] || shiftType;
  }

  /**
   * Utility function to get shift status display
   */
  getShiftStatusDisplay(status) {
    const statuses = {
      'scheduled': 'Scheduled',
      'active': 'Active',
      'completed': 'Completed',
      'cancelled': 'Cancelled'
    };
    
    return statuses[status] || status;
  }

  /**
   * Utility function to get shift status color
   */
  getShiftStatusColor(status) {
    const colors = {
      'scheduled': 'text-blue-600 bg-blue-50',
      'active': 'text-green-600 bg-green-50',
      'completed': 'text-gray-600 bg-gray-50',
      'cancelled': 'text-red-600 bg-red-50'
    };
    
    return colors[status] || 'text-gray-600 bg-gray-50';
  }
}

// Create and export singleton instance
const shiftService = new ShiftService();
export default shiftService;
