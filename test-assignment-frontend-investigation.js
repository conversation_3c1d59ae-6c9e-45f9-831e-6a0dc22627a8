// ============================================================================
// Test File: Assignment Management Frontend Display Logic Investigation
// Purpose: Investigate frontend display logic and API response data structure
// Date: 2025-07-12
// ============================================================================

const axios = require('axios');
require('dotenv').config();

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5444/api';

async function investigateAssignmentFrontendLogic() {
  console.log('🔍 Assignment Management Frontend Display Logic Investigation');
  console.log('===========================================================\n');
  
  try {
    // 1. Test Assignment Management API Response
    console.log('1️⃣ Testing Assignment Management API Response...\n');
    
    const assignmentResponse = await axios.get(`${API_BASE_URL}/assignments`, {
      params: {
        page: 1,
        limit: 10
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📊 Assignment Management API Response:');
    console.log(`Status: ${assignmentResponse.status}`);
    console.log(`Total Assignments: ${assignmentResponse.data.data.length}`);
    
    // Find DT-100 assignment
    const dt100Assignment = assignmentResponse.data.data.find(assignment => 
      assignment.truck_number === 'DT-100'
    );
    
    if (dt100Assignment) {
      console.log('\n🚛 DT-100 Assignment Data Structure:');
      console.log(`   Assignment Code: ${dt100Assignment.assignment_code}`);
      console.log(`   Truck: ${dt100Assignment.truck_number} (${dt100Assignment.license_plate})`);
      console.log(`   Status: ${dt100Assignment.status}`);
      
      // Check driver data fields
      console.log('\n👤 Driver Data Fields:');
      console.log(`   assignment.driver_name: ${dt100Assignment.driver_name || 'NULL'}`);
      console.log(`   assignment.employee_id: ${dt100Assignment.employee_id || 'NULL'}`);
      console.log(`   assignment.current_shift_driver_name: ${dt100Assignment.current_shift_driver_name || 'NULL'}`);
      console.log(`   assignment.current_shift_employee_id: ${dt100Assignment.current_shift_employee_id || 'NULL'}`);
      console.log(`   assignment.current_shift_type: ${dt100Assignment.current_shift_type || 'NULL'}`);
      
      // Analyze data availability
      console.log('\n🔍 Data Availability Analysis:');
      const hasAssignmentDriver = !!dt100Assignment.driver_name;
      const hasCurrentShiftDriver = !!dt100Assignment.current_shift_driver_name;
      
      console.log(`   Has Assignment Driver: ${hasAssignmentDriver ? '✅ YES' : '❌ NO'}`);
      console.log(`   Has Current Shift Driver: ${hasCurrentShiftDriver ? '✅ YES' : '❌ NO'}`);
      
      if (hasCurrentShiftDriver) {
        console.log(`   ✅ GOOD: API provides current shift driver data`);
        console.log(`   🔍 Expected Frontend Display: "${dt100Assignment.current_shift_driver_name} 🟢 ${dt100Assignment.current_shift_type} Shift"`);
      } else {
        console.log(`   🔴 ISSUE: API not providing current shift driver data`);
      }
      
      if (hasAssignmentDriver && !hasCurrentShiftDriver) {
        console.log(`   ⚠️  FALLBACK: Should show "${dt100Assignment.driver_name} 🟡 No Active Shift"`);
      }
      
      // Simulate frontend display logic
      console.log('\n📱 Frontend Display Logic Simulation:');
      let displayDriver, badgeType, badgeColor;
      
      if (hasCurrentShiftDriver) {
        displayDriver = `${dt100Assignment.current_shift_driver_name} (${dt100Assignment.current_shift_employee_id})`;
        badgeType = `${dt100Assignment.current_shift_type} Shift`;
        badgeColor = '🟢 Green';
      } else if (hasAssignmentDriver) {
        displayDriver = `${dt100Assignment.driver_name} (${dt100Assignment.employee_id})`;
        badgeType = 'No Active Shift';
        badgeColor = '🟡 Amber';
      } else {
        displayDriver = 'No driver assigned';
        badgeType = 'Warning';
        badgeColor = '🔴 Red';
      }
      
      console.log(`   Expected Display: ${displayDriver}`);
      console.log(`   Expected Badge: ${badgeColor} - ${badgeType}`);
      
    } else {
      console.log('   ❌ DT-100 assignment not found in API response');
    }
    
    // 2. Test Trip Monitoring API for comparison
    console.log('\n2️⃣ Testing Trip Monitoring API for Comparison...\n');
    
    const tripResponse = await axios.get(`${API_BASE_URL}/trips`, {
      params: {
        page: 1,
        limit: 10
      },
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📊 Trip Monitoring API Response:');
    console.log(`Status: ${tripResponse.status}`);
    console.log(`Total Trips: ${tripResponse.data.data.length}`);
    
    // Find DT-100 trip
    const dt100Trip = tripResponse.data.data.find(trip => 
      trip.truck_number === 'DT-100'
    );
    
    if (dt100Trip) {
      console.log('\n🚛 DT-100 Trip Data Structure:');
      console.log(`   Trip Number: ${dt100Trip.trip_number}`);
      console.log(`   Truck: ${dt100Trip.truck_number} (${dt100Trip.license_plate})`);
      console.log(`   Status: ${dt100Trip.status}`);
      
      // Check driver data fields
      console.log('\n👤 Driver Data Fields:');
      console.log(`   trip.driver_name: ${dt100Trip.driver_name || 'NULL'}`);
      console.log(`   trip.employee_id: ${dt100Trip.employee_id || 'NULL'}`);
      console.log(`   trip.current_shift_driver_name: ${dt100Trip.current_shift_driver_name || 'NULL'}`);
      console.log(`   trip.current_shift_employee_id: ${dt100Trip.current_shift_employee_id || 'NULL'}`);
      console.log(`   trip.current_shift_type: ${dt100Trip.current_shift_type || 'NULL'}`);
      console.log(`   trip.performed_by_driver_name: ${dt100Trip.performed_by_driver_name || 'NULL'}`);
      
      // Compare with assignment data
      console.log('\n🔄 Cross-System Data Comparison:');
      if (dt100Assignment && dt100Trip) {
        const assignmentShiftDriver = dt100Assignment.current_shift_driver_name;
        const tripShiftDriver = dt100Trip.current_shift_driver_name;
        
        console.log(`   Assignment Management Current Shift Driver: ${assignmentShiftDriver || 'NULL'}`);
        console.log(`   Trip Monitoring Current Shift Driver: ${tripShiftDriver || 'NULL'}`);
        console.log(`   Data Consistent: ${assignmentShiftDriver === tripShiftDriver ? '✅ YES' : '❌ NO'}`);
        
        if (assignmentShiftDriver === tripShiftDriver && assignmentShiftDriver) {
          console.log(`   ✅ GOOD: Both APIs provide consistent current shift driver data`);
          console.log(`   🔍 Issue is likely in Assignment Management frontend display logic`);
        }
      }
    }
    
    // 3. API Response Structure Analysis
    console.log('\n3️⃣ API Response Structure Analysis...\n');
    
    if (dt100Assignment) {
      console.log('📋 Assignment Management API Fields:');
      Object.keys(dt100Assignment).forEach(key => {
        if (key.includes('driver') || key.includes('shift')) {
          console.log(`   ${key}: ${dt100Assignment[key] || 'NULL'}`);
        }
      });
    }
    
    if (dt100Trip) {
      console.log('\n📋 Trip Monitoring API Fields:');
      Object.keys(dt100Trip).forEach(key => {
        if (key.includes('driver') || key.includes('shift')) {
          console.log(`   ${key}: ${dt100Trip[key] || 'NULL'}`);
        }
      });
    }
    
    // 4. Frontend Logic Requirements
    console.log('\n4️⃣ Frontend Logic Requirements...\n');
    
    console.log('🔧 Required Assignment Management Frontend Logic:');
    console.log('   1. Priority: current_shift_driver_name (if exists) → Green badge');
    console.log('   2. Fallback: driver_name (assignment driver) → Amber badge');
    console.log('   3. Default: "No driver assigned" → Red warning');
    
    console.log('\n📱 Expected Display for DT-100:');
    if (dt100Assignment?.current_shift_driver_name) {
      console.log(`   ✅ Should show: "${dt100Assignment.current_shift_driver_name} (${dt100Assignment.current_shift_employee_id}) 🟢 ${dt100Assignment.current_shift_type} Shift"`);
    } else if (dt100Assignment?.driver_name) {
      console.log(`   ⚠️  Should show: "${dt100Assignment.driver_name} (${dt100Assignment.employee_id}) 🟡 No Active Shift"`);
    } else {
      console.log(`   🔴 Should show: "No driver assigned ⚠️"`);
    }
    
    console.log('\n📋 Investigation Summary:');
    console.log('   1. Check if Assignment Management API provides current_shift_driver_name');
    console.log('   2. Compare Assignment Management and Trip Monitoring API responses');
    console.log('   3. Identify frontend display logic gap in AssignmentsTable.js');
    console.log('   4. Update frontend to prioritize current_shift_driver_name over driver_name');
    
  } catch (error) {
    console.error('❌ Investigation failed:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

// Run investigation if this file is executed directly
if (require.main === module) {
  investigateAssignmentFrontendLogic();
}

module.exports = { investigateAssignmentFrontendLogic };
