-- ============================================================================
-- Migration: Sync shift_date with start_date (Compatibility Approach)
-- Purpose: Make shift_date always equal start_date for unified approach compatibility
-- Date: 2025-07-11
-- ============================================================================

-- Update all existing shifts to have shift_date = start_date
UPDATE driver_shifts 
SET shift_date = start_date 
WHERE shift_date IS NULL OR shift_date != start_date;

-- Add constraint to ensure shift_date always equals start_date
ALTER TABLE driver_shifts 
ADD CONSTRAINT shift_date_equals_start_date CHECK (shift_date = start_date);

-- Create trigger to automatically sync shift_date with start_date
CREATE OR REPLACE FUNCTION sync_shift_date_with_start_date()
RETURNS TRIGGER AS $$
BEGIN
    -- Always set shift_date to equal start_date
    NEW.shift_date := NEW.start_date;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for INSERT and UPDATE
DROP TRIGGER IF EXISTS trigger_sync_shift_date ON driver_shifts;
CREATE TRIGGER trigger_sync_shift_date
    BEFORE INSERT OR UPDATE ON driver_shifts
    FOR EACH ROW
    EXECUTE FUNCTION sync_shift_date_with_start_date();

-- Update the shift creation function to handle unified approach
CREATE OR REPLACE FUNCTION create_unified_shift(
    p_truck_id INTEGER,
    p_driver_id INTEGER,
    p_shift_type shift_type,
    p_start_date DATE,
    p_end_date DATE,
    p_start_time TIME,
    p_end_time TIME,
    p_status shift_status DEFAULT 'scheduled',
    p_handover_notes TEXT DEFAULT ''
) RETURNS INTEGER AS $$
DECLARE
    v_shift_id INTEGER;
BEGIN
    INSERT INTO driver_shifts (
        truck_id,
        driver_id,
        shift_type,
        shift_date,      -- Will be auto-set to start_date by trigger
        start_date,
        end_date,
        start_time,
        end_time,
        status,
        handover_notes,
        recurrence_pattern
    ) VALUES (
        p_truck_id,
        p_driver_id,
        p_shift_type,
        p_start_date,    -- This will be synced by trigger
        p_start_date,
        p_end_date,
        p_start_time,
        p_end_time,
        p_status,
        p_handover_notes,
        CASE WHEN p_start_date = p_end_date THEN 'single' ELSE 'custom' END
    ) RETURNING id INTO v_shift_id;
    
    RETURN v_shift_id;
END;
$$ LANGUAGE plpgsql;

-- Test the synchronization
DO $$
DECLARE
    test_count INTEGER;
    synced_count INTEGER;
    single_day_count INTEGER;
    multi_day_count INTEGER;
BEGIN
    -- Count total shifts
    SELECT COUNT(*) INTO test_count FROM driver_shifts;
    
    -- Count properly synced shifts
    SELECT COUNT(*) INTO synced_count 
    FROM driver_shifts 
    WHERE shift_date = start_date;
    
    -- Count single-day shifts
    SELECT COUNT(*) INTO single_day_count 
    FROM driver_shifts 
    WHERE start_date = end_date;
    
    -- Count multi-day shifts
    SELECT COUNT(*) INTO multi_day_count 
    FROM driver_shifts 
    WHERE start_date < end_date;
    
    RAISE NOTICE 'Shift date synchronization results:';
    RAISE NOTICE '- Total shifts: %', test_count;
    RAISE NOTICE '- Properly synced (shift_date = start_date): %', synced_count;
    RAISE NOTICE '- Single-day shifts: %', single_day_count;
    RAISE NOTICE '- Multi-day shifts: %', multi_day_count;
    
    IF synced_count = test_count THEN
        RAISE NOTICE '✅ All shifts properly synchronized';
    ELSE
        RAISE NOTICE '⚠️ % shifts need manual review', (test_count - synced_count);
    END IF;
END $$;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 033 completed successfully: shift_date synchronized with start_date';
    RAISE NOTICE '- Updated all existing shifts: shift_date = start_date';
    RAISE NOTICE '- Added constraint to enforce synchronization';
    RAISE NOTICE '- Created trigger for automatic synchronization';
    RAISE NOTICE '- Single day shifts: shift_date = start_date = end_date';
    RAISE NOTICE '- Multi day shifts: shift_date = start_date, end_date different';
    RAISE NOTICE '- All existing queries using shift_date will now work correctly';
    RAISE NOTICE '- Frontend can display using either shift_date or start_date/end_date';
END $$;
