-- ============================================================================
-- Migration: Remove shift_date Column (Complete Unified Approach)
-- Purpose: Eliminate shift_date column since unified approach uses start_date/end_date
-- Date: 2025-07-11
-- ============================================================================

-- Remove any remaining constraints that reference shift_date
ALTER TABLE driver_shifts DROP CONSTRAINT IF EXISTS valid_shift_date_config;
ALTER TABLE driver_shifts DROP CONSTRAINT IF EXISTS unified_shift_date_config;

-- Drop indexes that use shift_date
DROP INDEX IF EXISTS idx_driver_shifts_date;
DROP INDEX IF EXISTS idx_driver_shifts_truck_date;
DROP INDEX IF EXISTS idx_driver_shifts_status_date;

-- Remove the shift_date column completely
ALTER TABLE driver_shifts DROP COLUMN IF EXISTS shift_date;

-- Add optimized indexes for unified approach
CREATE INDEX IF NOT EXISTS idx_driver_shifts_unified_date_range 
ON driver_shifts (truck_id, start_date, end_date, status);

CREATE INDEX IF NOT EXISTS idx_driver_shifts_start_date 
ON driver_shifts (start_date);

CREATE INDEX IF NOT EXISTS idx_driver_shifts_end_date 
ON driver_shifts (end_date);

-- Add constraint to ensure date range validity
ALTER TABLE driver_shifts
ADD CONSTRAINT valid_unified_date_range CHECK (
    start_date IS NOT NULL AND 
    end_date IS NOT NULL AND 
    start_date <= end_date
);

-- Update any views that might reference shift_date
-- (Note: We'll handle view updates in the application code)

-- Create helper function for date range queries
CREATE OR REPLACE FUNCTION shift_includes_date(
    p_start_date DATE,
    p_end_date DATE,
    p_query_date DATE
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN p_query_date BETWEEN p_start_date AND p_end_date;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create helper function for date range overlap
CREATE OR REPLACE FUNCTION shift_overlaps_range(
    p_start_date DATE,
    p_end_date DATE,
    p_range_start DATE,
    p_range_end DATE
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN p_start_date <= p_range_end AND p_end_date >= p_range_start;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Test the unified approach
DO $$
DECLARE
    test_count INTEGER;
    single_day_count INTEGER;
    multi_day_count INTEGER;
BEGIN
    -- Count total shifts
    SELECT COUNT(*) INTO test_count FROM driver_shifts;
    
    -- Count single-day shifts
    SELECT COUNT(*) INTO single_day_count 
    FROM driver_shifts 
    WHERE start_date = end_date;
    
    -- Count multi-day shifts
    SELECT COUNT(*) INTO multi_day_count 
    FROM driver_shifts 
    WHERE start_date < end_date;
    
    RAISE NOTICE 'Unified approach validation after shift_date removal:';
    RAISE NOTICE '- Total shifts: %', test_count;
    RAISE NOTICE '- Single-day shifts: %', single_day_count;
    RAISE NOTICE '- Multi-day shifts: %', multi_day_count;
    
    -- Test date filtering functions
    RAISE NOTICE 'Testing helper functions:';
    RAISE NOTICE '- shift_includes_date test: %', shift_includes_date('2025-07-11', '2025-07-15', '2025-07-13');
    RAISE NOTICE '- shift_overlaps_range test: %', shift_overlaps_range('2025-07-11', '2025-07-15', '2025-07-13', '2025-07-20');
END $$;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 032 completed successfully: shift_date column removed';
    RAISE NOTICE '- Removed shift_date column completely';
    RAISE NOTICE '- Added optimized indexes for unified approach';
    RAISE NOTICE '- Added helper functions for date queries';
    RAISE NOTICE '- All shifts now use pure start_date/end_date approach';
    RAISE NOTICE '- Single day: start_date = end_date';
    RAISE NOTICE '- Multi day: start_date < end_date';
END $$;
