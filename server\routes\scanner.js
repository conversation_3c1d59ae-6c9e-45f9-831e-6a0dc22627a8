const express = require('express');
const router = express.Router();
const { query, getClient } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const { notifyTripStatusChanged } = require('../websocket');
const { logger, EnhancedLogger } = require('../utils/logger');
const { AutoAssignmentCreator } = require('../utils/AutoAssignmentCreator');
const { calculateDurations } = require('../utils/durationCalculator');

// Validation schemas
const scanSchema = Joi.object({
  scan_type: Joi.string().valid('location', 'truck').required(),
  scanned_data: Joi.string().required(),
  location_scan_data: Joi.object().when('scan_type', {
    is: 'truck',
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  ip_address: Joi.string().ip().optional(),
  user_agent: Joi.string().optional(),
  request_id: Joi.string().optional()
});

// Enhanced logging functions for critical operations only

function logError(context, error, additionalData = {}) {
  EnhancedLogger.logScanError(context, error, additionalData.scanData || {}, additionalData.userInfo || {});
}

// Helper function to capture active driver for historical tracking
async function captureActiveDriverInfo(client, truckId, timestamp = new Date()) {
  try {
    const driverResult = await client.query(`
      SELECT * FROM capture_active_driver_for_trip($1, $2)
    `, [truckId, timestamp]);

    if (driverResult.rows.length > 0) {
      const driver = driverResult.rows[0];
      logDebug('DRIVER_CAPTURE', 'Active driver captured for trip', {
        truck_id: truckId,
        driver_id: driver.driver_id,
        driver_name: driver.driver_name,
        shift_type: driver.shift_type,
        timestamp: timestamp.toISOString()
      });
      return driver;
    } else {
      logDebug('DRIVER_CAPTURE', 'No active driver found for truck', {
        truck_id: truckId,
        timestamp: timestamp.toISOString()
      });
      return null;
    }
  } catch (error) {
    logError('DRIVER_CAPTURE', error, {
      truck_id: truckId,
      timestamp: timestamp.toISOString()
    });
    return null;
  }
}

// @route   POST /api/scanner/scan
// @desc    Process QR code scan with enhanced error handling and transaction management
// @access  Private
router.post('/scan', auth, async (req, res) => {
  const client = await getClient();
  const scanStartTime = Date.now();
  const requestId = req.body.request_id || `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  try {
    // Enhanced validation with performance tracking
    const validationStart = Date.now();
    const { error } = scanSchema.validate(req.body);
    if (error) {
      logError('SCAN_VALIDATION', error, {
        body: req.body,
        request_id: requestId,
        validation_time: Date.now() - validationStart
      });
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: error.details[0].message,
        request_id: requestId
      });
    }

    logDebug('SCAN_VALIDATION_SUCCESS', 'Input validation completed', {
      request_id: requestId,
      validation_time: Date.now() - validationStart
    });

    const {
      scan_type,
      scanned_data,
      location_scan_data,
      ip_address,
      user_agent
    } = req.body;

    logDebug('SCAN_REQUEST', 'Processing scan', {
      scan_type,
      user_id: req.user.id,
      ip_address
    });

    // Parse scanned QR data
    let qrData;
    try {
      if (!scanned_data || typeof scanned_data !== 'string') {
        throw new Error('Scanned data is empty or not a string');
      }
      qrData = JSON.parse(scanned_data);
    } catch (parseError) {
      logError('QR_PARSE', parseError, {
        scanData: {
          scan_type,
          scanned_data,
          ip_address,
          user_agent
        },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: parseError.message === 'Scanned data is empty or not a string'
          ? 'QR code data is empty or invalid'
          : 'QR code data is not valid JSON format'
      });
    }

    // Validate QR code structure
    if (!qrData || typeof qrData !== 'object') {
      logError('QR_VALIDATION', new Error('QR data is not an object'), {
        scanData: { scan_type, scanned_data: JSON.stringify(qrData) },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: 'QR code data must be a valid object'
      });
    }

    if (!qrData.type || !qrData.id) {
      logError('QR_VALIDATION', new Error('Missing required fields'), {
        scanData: {
          scan_type,
          scanned_data: JSON.stringify(qrData),
          missing_fields: {
            type: !qrData.type,
            id: !qrData.id
          }
        },
        userInfo: req.user
      });
      return res.status(400).json({
        success: false,
        error: 'Invalid QR Code',
        message: 'QR code must contain type and id fields'
      });
    }

    // Verify QR type matches scan type
    if (qrData.type !== scan_type) {
      return res.status(400).json({
        success: false,
        error: 'QR Type Mismatch',
        message: `Expected ${scan_type} QR code but scanned ${qrData.type}`
      });
    }

    // Start database transaction with performance tracking
    const transactionStart = Date.now();
    await client.query('BEGIN');
    logDebug('TRANSACTION', 'Transaction started', {
      request_id: requestId,
      transaction_start_time: Date.now() - scanStartTime
    });

    let scanResult;
    const processingStart = Date.now();

    if (scan_type === 'location') {
      scanResult = await processLocationScan(client, qrData, req.user.id, ip_address, user_agent, requestId);
    } else if (scan_type === 'truck') {
      scanResult = await processTruckScan(client, qrData, location_scan_data, req.user.id, ip_address, user_agent, requestId);
    }

    const scanProcessingTime = Date.now() - processingStart;
    logDebug('SCAN_PROCESSING_COMPLETE', 'Scan processing completed', {
      request_id: requestId,
      scan_type,
      processing_time: scanProcessingTime,
      total_time_so_far: Date.now() - scanStartTime
    });

    // Log successful scan within the transaction
    await logScan(client, {
      scan_type,
      scanned_data,
      scanner_user_id: req.user.id,
      scanned_location_id: scanResult.location_id || null,
      scanned_truck_id: scanResult.truck_id || null,
      trip_log_id: scanResult.trip_log_id || null,
      is_valid: true,
      ip_address,
      user_agent
    });

    // Commit transaction
    await client.query('COMMIT');
    logDebug('TRANSACTION', 'Transaction committed successfully');

    const processingTime = Date.now() - scanStartTime;
    logDebug('SCAN_SUCCESS', `Scan processed successfully in ${processingTime}ms`, {
      scanResult,
      processingTime
    });

    res.json({
      success: true,
      message: scanResult.message,
      data: scanResult.data,
      next_step: scanResult.next_step,
      processing_time_ms: processingTime
    });

  } catch (error) {
    // Enhanced transaction rollback with connection recovery
    try {
      await client.query('ROLLBACK');
      logDebug('TRANSACTION_ROLLBACK', 'Transaction rolled back successfully');
    } catch (rollbackError) {
      logError('TRANSACTION_ROLLBACK_ERROR', rollbackError, {
        original_error: error.message,
        rollback_error: rollbackError.message
      });

      // If rollback fails, the connection might be corrupted
      // Release the client and get a new one for error logging
      try {
        client.release();
      } catch (releaseError) {
        console.error('Failed to release corrupted client:', releaseError);
      }
    }

    logError('SCAN_ERROR', error, {
      scanData: {
        scan_type: req.body.scan_type,
        scanned_data: req.body.scanned_data,
        ip_address: req.body.ip_address,
        user_agent: req.body.user_agent
      },
      userInfo: req.user,
      error_type: error.constructor.name,
      error_code: error.code
    });

    // Log failed scan with a new client connection to avoid transaction state issues
    try {
      const { getClient } = require('../config/database');
      const logClient = await getClient();
      try {
        await logScan(logClient, {
          scan_type: req.body.scan_type,
          scanned_data: req.body.scanned_data,
          scanner_user_id: req.user?.id,
          is_valid: false,
          validation_error: error.message,
          ip_address: req.body.ip_address,
          user_agent: req.body.user_agent
        });
      } finally {
        logClient.release();
      }
    } catch (logError) {
      console.error('Failed to log scan error:', logError);
    }

    res.status(500).json({
      success: false,
      error: 'Scan Processing Error',
      message: error.message || 'Failed to process scan',
      processing_time_ms: Date.now() - scanStartTime,
      location_data: error.location_data || null
    });
  } finally {
    client.release();
  }
});

// Process location QR scan with optimized queries
async function processLocationScan(client, qrData, userId, ipAddress, userAgent) {
  // Optimized query with only needed columns
  const locationResult = await client.query(
    `SELECT id, location_code, name, type, coordinates, qr_code_data
     FROM locations
     WHERE location_code = $1 AND status = 'active'`,
    [qrData.id]
  );

  if (locationResult.rows.length === 0) {
    throw new Error(`Location ${qrData.id} not found or inactive`);
  }

  const location = locationResult.rows[0];

  // Verify QR code data matches database (JSONB is already parsed)
  const storedQrData = location.qr_code_data;

  if (!storedQrData || typeof storedQrData !== 'object') {
    throw new Error('Invalid QR code data in database');
  }

  if (storedQrData.id !== qrData.id) {
    throw new Error('QR code data mismatch with database');
  }

  return {
    message: `Location ${location.name} scanned successfully`,
    location_id: location.id,
    data: {
      location: {
        id: location.id,
        code: location.location_code,
        name: location.name,
        type: location.type,
        coordinates: location.coordinates
      }
    },
    next_step: 'scan_truck'
  };
}

// Process truck QR scan with enhanced assignment validation
async function processTruckScan(client, qrData, locationScanData, userId, ipAddress, userAgent) {
  // =====================================================================
  // ENHANCED: Simplified assignment-based approach
  // 1. Comprehensive assignment validation for all scenarios
  // 2. AutoAssignmentCreator integration for seamless assignment resolution
  // 3. Flexible trip progression without exception interruptions
  // =====================================================================

  try {
    logDebug('TRUCK_SCAN_START', 'Starting truck scan processing', {
      truck_number: qrData.id,
      location_code: locationScanData?.id,
      user_id: userId
    });

    // Validate truck exists and is active
    let truckResult;
    try {
      logDebug('TRUCK_VALIDATION_QUERY', 'Executing truck validation query', {
        truck_number: qrData.id,
        status: 'active'
      });

      truckResult = await client.query(
        `SELECT id, truck_number, license_plate, qr_code_data, status
         FROM dump_trucks
         WHERE truck_number = $1 AND status = $2`,
        [qrData.id, 'active']
      );

      logDebug('TRUCK_VALIDATION_SUCCESS', 'Truck validation query completed', {
        truck_number: qrData.id,
        trucks_found: truckResult.rows.length
      });

    } catch (truckValidationError) {
      logError('TRUCK_VALIDATION_ERROR', truckValidationError, {
        truck_number: qrData.id,
        error_code: truckValidationError.code,
        error_detail: truckValidationError.detail
      });
      throw new Error(`Truck validation query failed: ${truckValidationError.message}`);
    }

  if (truckResult.rows.length === 0) {
    const err = new Error(`Truck ${qrData.id} not found or inactive`);
    if (locationScanData) {
      err.location_data = locationScanData;
    }
    throw err;
  }

  const truck = truckResult.rows[0];

  // Verify QR code data (JSONB is already parsed)
  const storedQrData = truck.qr_code_data;

  if (!storedQrData || typeof storedQrData !== 'object') {
    throw new Error('Invalid truck QR code data in database');
  }

  if (storedQrData.id !== qrData.id) {
    throw new Error('Truck QR code data mismatch with database');
  }

  // Get location from previous scan
  let locationResult;
  try {
    logDebug('LOCATION_VALIDATION_QUERY', 'Executing location validation query', {
      location_code: locationScanData.id
    });

    locationResult = await client.query(
      `SELECT id, location_code, name, type
       FROM locations
       WHERE location_code = $1`,
      [locationScanData.id]
    );

    logDebug('LOCATION_VALIDATION_SUCCESS', 'Location validation query completed', {
      location_code: locationScanData.id,
      locations_found: locationResult.rows.length
    });

  } catch (locationValidationError) {
    logError('LOCATION_VALIDATION_ERROR', locationValidationError, {
      location_code: locationScanData.id,
      error_code: locationValidationError.code,
      error_detail: locationValidationError.detail
    });
    throw new Error(`Location validation query failed: ${locationValidationError.message}`);
  }

  if (locationResult.rows.length === 0) {
    const err = new Error('Invalid location context for truck scan');
    err.location_data = locationScanData; // Preserve the original scan data
    throw err;
  }

  const location = locationResult.rows[0];

  // Get current trip and assignment with optimized query
  let tripData;
  try {
    logDebug('TRIP_ASSIGNMENT_QUERY', 'Executing getCurrentTripAndAssignment query', {
      truck_id: truck.id,
      truck_number: truck.truck_number
    });

    tripData = await getCurrentTripAndAssignment(client, truck.id);

    logDebug('TRIP_ASSIGNMENT_SUCCESS', 'getCurrentTripAndAssignment completed successfully', {
      truck_id: truck.id,
      has_trip: !!tripData.trip,
      has_assignment: !!tripData.assignment,
      trip_status: tripData.trip?.status,
      assignment_id: tripData.assignment?.id
    });

  } catch (tripAssignmentError) {
    logError('TRIP_ASSIGNMENT_ERROR', tripAssignmentError, {
      truck_id: truck.id,
      truck_number: truck.truck_number,
      error_code: tripAssignmentError.code,
      error_detail: tripAssignmentError.detail
    });
    throw new Error(`Trip assignment query failed: ${tripAssignmentError.message}`);
  }

  // REMOVED: Problematic A→B→C workflow trigger that interfered with normal operations
  // A→B→C workflow should only trigger through normal trip completion, not here
  
  // Assignment lookup completed

  // MULTI-LOCATION WORKFLOW: Check for post-completion loading scenarios
  // CRITICAL FIX: Check for post-completion scenarios REGARDLESS of active trip status
  // This ensures A→B→C workflows are detected even if there are active trips
  let recentCompletedTrip = null;
  try {
    recentCompletedTrip = await checkRecentCompletedTrip(client, truck.id, location);
  } catch (completedTripError) {
    logError('COMPLETED_TRIP_CHECK_ERROR', completedTripError, {
      truck_id: truck.id,
      location_id: location.id,
      error_code: completedTripError.code
    });
    // Continue without recent completed trip check if this fails
    recentCompletedTrip = null;
  }

  logDebug('POST_COMPLETION_CHECK', 'Checking for recent completed trips', {
    truck_number: truck.truck_number,
    truck_id: truck.id,
    location_name: location.name,
    location_type: location.type,
    has_active_trip: !!tripData.trip,
    active_trip_status: tripData.trip?.status,
    has_recent_completed_trip: !!recentCompletedTrip,
    recent_trip_id: recentCompletedTrip?.id,
    recent_trip_status: recentCompletedTrip?.status,
    recent_trip_completed_time: recentCompletedTrip?.trip_completed_time
  });

  // CRITICAL FIX: Only trigger post-completion logic when there's NO active trip
  // If there's an active trip, it should follow the 4-phase workflow progression
  if (recentCompletedTrip && location.type === 'loading' && !tripData.trip) {
    logDebug('POST_COMPLETION_DETECTION', 'Detected post-completion loading scenario', {
      truck_number: truck.truck_number,
      completed_trip_id: recentCompletedTrip.id,
      completed_trip_route: `${recentCompletedTrip.loading_location_name} → ${recentCompletedTrip.unloading_location_name}`,
      new_location: location.name,
      location_type: location.type,
      workflow_opportunity: 'extension_or_cycle',
      has_active_trip: !!tripData.trip,
      workflow_priority: 'Post-completion only when no active trip exists'
    });

    // Handle post-completion loading scenario
    const workflowResult = await handlePostCompletionLoading(
      client,
      recentCompletedTrip,
      location,
      truck,
      userId
    );

    if (workflowResult) {
      logDebug('POST_COMPLETION_SUCCESS', 'Workflow created successfully, returning result', {
        workflow_type: workflowResult.workflow_type,
        new_trip_id: workflowResult.trip?.id,
        message: workflowResult.message
      });
      return workflowResult;
    }
  }

  // Continue with normal trip processing if no workflow was created
  if (!tripData.trip) {
    // ENHANCED ASSIGNMENT VALIDATION: Comprehensive check for all valid assignment scenarios
    logDebug('ENHANCED_VALIDATION', 'No active trip found - performing comprehensive assignment validation', {
      truck_number: truck.truck_number,
      location_name: location.name,
      location_type: location.type,
      location_id: location.id
    });

    // Step 1: Check for ALL assignments where current location is included (loading OR unloading)
    let allValidAssignments;
    try {
      logDebug('ASSIGNMENT_QUERY', 'Executing assignment validation query', {
        truck_number: qrData.id,
        location_id: location.id,
        query_params: [qrData.id, location.id]
      });

      allValidAssignments = await client.query(`
        SELECT
          a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
          a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
          dt.truck_number, dt.status as truck_status,
          ll.name as loading_location, ul.name as unloading_location,
          d.full_name as driver_name,
          CASE
            WHEN a.loading_location_id = $2 THEN 'loading'
            WHEN a.unloading_location_id = $2 THEN 'unloading'
            ELSE 'none'
          END as location_role
        FROM assignments a
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN drivers d ON a.driver_id = d.id
        WHERE dt.truck_number = $1
          AND a.status = 'assigned'
          AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
        ORDER BY a.created_at DESC
      `, [qrData.id, location.id]);

      logDebug('ASSIGNMENT_QUERY_SUCCESS', 'Assignment query completed successfully', {
        assignments_found: allValidAssignments.rows.length,
        truck_number: qrData.id,
        location_id: location.id
      });

    } catch (queryError) {
      logError('ASSIGNMENT_QUERY_ERROR', queryError, {
        truck_number: qrData.id,
        location_id: location.id,
        query_params: [qrData.id, location.id],
        error_code: queryError.code,
        error_detail: queryError.detail
      });

      // CRITICAL FIX: Handle transaction abort state
      if (queryError.message && queryError.message.includes('current transaction is aborted')) {
        logError('TRANSACTION_ABORT_DETECTED', 'Transaction is in aborted state, attempting recovery', {
          truck_number: qrData.id,
          location_id: location.id
        });

        // Try to rollback and restart the query with a new client
        try {
          await client.query('ROLLBACK');
          await client.query('BEGIN');

          // Retry the query
          allValidAssignments = await client.query(`
            SELECT
              a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
              a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
              dt.truck_number, dt.status as truck_status,
              ll.name as loading_location, ul.name as unloading_location,
              d.full_name as driver_name,
              CASE
                WHEN a.loading_location_id = $2 THEN 'loading'
                WHEN a.unloading_location_id = $2 THEN 'unloading'
                ELSE 'none'
              END as location_role
            FROM assignments a
            JOIN dump_trucks dt ON a.truck_id = dt.id
            LEFT JOIN locations ll ON a.loading_location_id = ll.id
            LEFT JOIN locations ul ON a.unloading_location_id = ul.id
            LEFT JOIN drivers d ON a.driver_id = d.id
            WHERE dt.truck_number = $1
              AND a.status = 'assigned'
              AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
            ORDER BY a.created_at DESC
          `, [qrData.id, location.id]);

          logDebug('ASSIGNMENT_QUERY_RECOVERY_SUCCESS', 'Assignment query recovered successfully after transaction abort', {
            assignments_found: allValidAssignments.rows.length,
            truck_number: qrData.id,
            location_id: location.id
          });

        } catch (recoveryError) {
          logError('ASSIGNMENT_QUERY_RECOVERY_FAILED', recoveryError, {
            truck_number: qrData.id,
            location_id: location.id
          });
          throw new Error(`Assignment validation query failed after recovery attempt: ${recoveryError.message}`);
        }
      } else {
        throw new Error(`Assignment validation query failed: ${queryError.message}`);
      }
    }

    if (allValidAssignments.rows.length > 0) {
      // FOUND VALID ASSIGNMENT: Use the most recent assignment for this location
      const validAssignment = allValidAssignments.rows[0];

      // Check if this is a dynamic assignment that needs updating for route discovery
      const assignmentNotes = validAssignment.notes ? JSON.parse(validAssignment.notes) : {};
      const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';

      if (isDynamicAssignment) {
        logDebug('DYNAMIC_ASSIGNMENT', 'Checking dynamic assignment for route discovery update', {
          assignment_id: validAssignment.id,
          assignment_code: validAssignment.assignment_code,
          current_loading: validAssignment.loading_location_id,
          current_unloading: validAssignment.unloading_location_id,
          scan_location: location.name,
          scan_location_type: location.type
        });

        // Check if we need to update the dynamic assignment with new location
        const needsUpdate = (!validAssignment.loading_location_id && location.type === 'loading') ||
                           (!validAssignment.unloading_location_id && location.type === 'unloading');

        if (needsUpdate) {
          const autoAssignmentCreator = new AutoAssignmentCreator();
          const updatedAssignment = await autoAssignmentCreator.updateDynamicAssignment({
            assignment: validAssignment,
            location,
            client
          });

          logDebug('DYNAMIC_ASSIGNMENT', 'Dynamic assignment updated with discovered location', {
            assignment_id: updatedAssignment.id,
            assignment_code: updatedAssignment.assignment_code,
            updated_loading: updatedAssignment.loading_location_id,
            updated_unloading: updatedAssignment.unloading_location_id,
            discovered_location: location.name
          });

          // Update the assignment object with new location data
          validAssignment.loading_location_id = updatedAssignment.loading_location_id;
          validAssignment.unloading_location_id = updatedAssignment.unloading_location_id;
        }
      }

      // CRITICAL VALIDATION: Verify assignment role matches location type
      if (validAssignment.location_role === 'loading' && location.type !== 'loading') {
        const errorMessage = `Assignment role mismatch: Assignment expects loading operation but location "${location.name}" is type "${location.type}". Loading operations must be performed at loading-type locations.`;
        logError('ASSIGNMENT_ROLE_VALIDATION_ERROR', new Error(errorMessage), {
          assignment_id: validAssignment.id,
          assignment_role: validAssignment.location_role,
          location_name: location.name,
          location_type: location.type,
          truck_number: truck.truck_number
        });
        throw new Error(errorMessage);
      }

      if (validAssignment.location_role === 'unloading' && location.type !== 'unloading') {
        const errorMessage = `Assignment role mismatch: Assignment expects unloading operation but location "${location.name}" is type "${location.type}". Unloading operations must be performed at unloading-type locations.`;
        logError('ASSIGNMENT_ROLE_VALIDATION_ERROR', new Error(errorMessage), {
          assignment_id: validAssignment.id,
          assignment_role: validAssignment.location_role,
          location_name: location.name,
          location_type: location.type,
          truck_number: truck.truck_number
        });
        throw new Error(errorMessage);
      }

      logDebug('ENHANCED_VALIDATION', 'Valid assignment found and location type validated', {
        truck_number: truck.truck_number,
        location_name: location.name,
        location_type: location.type,
        assignment_id: validAssignment.id,
        assignment_code: validAssignment.assignment_code,
        location_role: validAssignment.location_role,
        is_dynamic: isDynamicAssignment,
        validation_passed: true
      });

      EnhancedLogger.logAssignmentLookup(truck.id, {
        truck_number: qrData.id,
        location_id: location.id,
        location_name: location.name,
        assignment_found: true,
        assignment_id: validAssignment.id,
        location_role: validAssignment.location_role,
        dynamic_assignment: isDynamicAssignment,
        route_discovery: isDynamicAssignment ? 'progressive' : 'complete'
      }, [validAssignment], 'VALID_ASSIGNMENT_FOUND');

      // Use the valid assignment to create a new trip
      return await handleNewTrip(
        client,
        validAssignment,
        location,
        truck,
        userId,
        new Date()
      );
    }

    // Step 2: No valid assignment found - Try auto-assignment creation
    logDebug('ENHANCED_VALIDATION', 'No valid assignment found - attempting auto-assignment creation', {
      truck_number: truck.truck_number,
      location_name: location.name,
      location_type: location.type
    });

    const autoAssignmentCreator = new AutoAssignmentCreator();

    try {
      // Check if auto-assignment creation is appropriate
      const shouldCreateCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
        truck,
        location,
        client
      });

      if (shouldCreateCheck.shouldCreate) {
        logDebug('ENHANCED_VALIDATION', 'Creating auto-assignment for unassigned location', {
          truck_number: truck.truck_number,
          location_name: location.name,
          location_type: location.type,
          reason: shouldCreateCheck.reason
        });

        // Create dynamic auto-assignment with progressive route discovery
        const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
          truck,
          location,
          client,
          userId,
          enableDynamicRouting: true // Enable progressive route building
        });

        EnhancedLogger.logAssignmentLookup(truck.id, {
          truck_number: qrData.id,
          location_id: location.id,
          location_name: location.name,
          assignment_found: true,
          assignment_id: autoAssignment.id,
          auto_created: true
        }, [autoAssignment], 'AUTO_ASSIGNMENT_CREATED');

        // CRITICAL FIX: Instead of auto-completing and creating new trip,
        // use the new handleNewTrip function which will detect and update existing trips
        logDebug('ENHANCED_VALIDATION', 'Using auto-created assignment with trip deduplication', {
          auto_assignment_id: autoAssignment.id,
          truck_number: truck.truck_number,
          location_name: location.name,
          has_existing_trip: !!tripData.trip
        });

        // The handleNewTrip function now includes trip deduplication logic
        // It will automatically detect existing trips and update them instead of creating duplicates
        return await handleNewTrip(
          client,
          autoAssignment,
          location,
          truck,
          userId,
          new Date()
        );
      } else if (shouldCreateCheck.existingAssignment) {
        // ENHANCED: Reuse existing assignment instead of creating new one
        logDebug('ENHANCED_VALIDATION', 'Reusing existing assignment instead of creating new one', {
          truck_number: truck.truck_number,
          location_name: location.name,
          location_type: location.type,
          existing_assignment: shouldCreateCheck.existingAssignment.assignment_code,
          reason: shouldCreateCheck.reason
        });

        // Fetch the complete assignment data for reuse
        const existingAssignmentResult = await client.query(`
          SELECT
            a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
            a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
            a.notes,
            dt.truck_number, dt.status as truck_status,
            ll.name as loading_location, ul.name as unloading_location,
            d.full_name as driver_name
          FROM assignments a
          JOIN dump_trucks dt ON a.truck_id = dt.id
          LEFT JOIN locations ll ON a.loading_location_id = ll.id
          LEFT JOIN locations ul ON a.unloading_location_id = ul.id
          LEFT JOIN drivers d ON a.driver_id = d.id
          WHERE a.id = $1
        `, [shouldCreateCheck.existingAssignment.id]);

        if (existingAssignmentResult.rows.length > 0) {
          const reusedAssignment = existingAssignmentResult.rows[0];

          // Update assignment status to 'in_progress' if it was completed
          if (reusedAssignment.status === 'completed') {
            await client.query(
              `UPDATE assignments SET status = 'in_progress', updated_at = CURRENT_TIMESTAMP WHERE id = $1`,
              [reusedAssignment.id]
            );
            reusedAssignment.status = 'in_progress';
          }

          EnhancedLogger.logAssignmentLookup(truck.id, {
            truck_number: qrData.id,
            location_id: location.id,
            location_name: location.name,
            assignment_found: true,
            assignment_id: reusedAssignment.id,
            reused_existing: true
          }, [reusedAssignment], 'EXISTING_ASSIGNMENT_REUSED');

          // Use the reused assignment to create a new trip
          return await handleNewTrip(
            client,
            reusedAssignment,
            location,
            truck,
            userId,
            new Date()
          );
        }
      } else {
        logDebug('ENHANCED_VALIDATION', 'Auto-assignment creation not appropriate', {
          truck_number: truck.truck_number,
          location_name: location.name,
          reason: shouldCreateCheck.reason,
          recommendation: shouldCreateCheck.recommendation
        });

        // Return error with clear message
        throw new Error(`Cannot create assignment for truck ${truck.truck_number} at ${location.name}. ${shouldCreateCheck.reason}. ${shouldCreateCheck.recommendation}`);
      }
    } catch (autoAssignmentError) {
      logError('ENHANCED_VALIDATION', autoAssignmentError, {
        truck_number: truck.truck_number,
        location_name: location.name
      });

      // Return error with clear message
      throw new Error(`Failed to create assignment for truck ${truck.truck_number} at ${location.name}: ${autoAssignmentError.message}`);
    }
  }

  // ENHANCED: Assignment validation logic ensures comprehensive coverage above

  // CRITICAL FIX: Prioritize trip progression over assignment changes
  // If there's an active trip, check if it can be progressed or needs new trip
  if (tripData.trip) {
    logDebug('TRIP_PROGRESSION', 'Active trip found - checking trip status', {
      trip_id: tripData.trip.id,
      trip_status: tripData.trip.status,
      assignment_id: tripData.assignment?.id,
      location_name: location.name,
      truck_number: truck.truck_number
    });

    // Check if trip is in a terminal state (stopped or completed)
    if (tripData.trip.status === 'stopped') {
      logDebug('TRIP_PROGRESSION', 'Stopped trip found - using trip completion logic', {
        trip_id: tripData.trip.id,
        trip_status: tripData.trip.status,
        action: 'handle_as_trip_completed'
      });

      // CRITICAL FIX: Stopped trips should create new trips WITHOUT modifying stopped status
      // Use specialized function that preserves stopped status
      return await handleStoppedNewTrip(
        client,
        tripData.trip,
        tripData.assignment,
        location,
        truck,
        userId,
        new Date()
      );
    } else if (tripData.trip.status === 'trip_completed' || tripData.trip.status === 'cancelled') {
      logDebug('TRIP_PROGRESSION', 'Completed/cancelled trip found - using trip completion logic', {
        trip_id: tripData.trip.id,
        trip_status: tripData.trip.status,
        action: 'handle_as_trip_completed'
      });

      // Use the same handleUnloadingEnd function for completed trips
      return await handleUnloadingEnd(
        client,
        tripData.trip,
        tripData.assignment,
        location,
        truck,
        userId,
        new Date()
      );
    } else {
      // Trip is active - use workflow progression logic
      logDebug('TRIP_PROGRESSION', 'Trip is active - using workflow progression logic', {
        trip_id: tripData.trip.id,
        trip_status: tripData.trip.status
      });

      // Use the trip's assignment for progression, not the newly found assignment
      const tripAssignment = tripData.assignment || {
        id: tripData.trip.assignment_id,
        loading_location_id: tripData.trip.loading_location_id,
        unloading_location_id: tripData.trip.unloading_location_id
      };

      const actionResult = await determineNextAction(
        client,
        tripData.trip,
        tripAssignment,
        location,
        truck,
        userId
      );

      return {
        message: actionResult.message,
        truck_id: truck.id,
        location_id: location.id,
        trip_log_id: actionResult.trip_log_id,
        data: {
          truck: {
            id: truck.id,
            number: truck.truck_number,
            license_plate: truck.license_plate
          },
          location: {
            id: location.id,
            code: location.location_code,
            name: location.name,
            type: location.type
          },
          trip: actionResult.trip_data,
          assignment: tripAssignment ? {
            id: tripAssignment.id,
            loading_location: tripAssignment.loading_location_name,
            unloading_location: tripAssignment.unloading_location_name
          } : null
        },
        next_step: actionResult.next_step
      };
    }
  }

  // Create new trip if: no trip exists OR existing trip is in terminal state
  const shouldCreateNewTrip = !tripData.trip ||
    (tripData.trip && ['stopped', 'trip_completed', 'cancelled'].includes(tripData.trip.status));

  if (shouldCreateNewTrip && tripData.assignment) {
    const reason = !tripData.trip ? 'No active trip found' :
      `Previous trip in terminal state (${tripData.trip.status})`;

    // Check if the assignment is compatible with the current location
    let assignmentLocationRole = 'none';
    if (tripData.assignment.loading_location_id === location.id) {
      assignmentLocationRole = 'loading';
    } else if (tripData.assignment.unloading_location_id === location.id) {
      assignmentLocationRole = 'unloading';
    }

    const isCompatibleAssignment =
      (assignmentLocationRole === 'loading' && location.type === 'loading') ||
      (assignmentLocationRole === 'unloading' && location.type === 'unloading');

    if (isCompatibleAssignment) {
      // Assignment is compatible - use it directly
      logDebug('NEW_TRIP_CREATION', `${reason} - creating new trip with compatible assignment`, {
        assignment_id: tripData.assignment.id,
        location_name: location.name,
        truck_number: truck.truck_number,
        previous_trip_id: tripData.trip?.id,
        previous_trip_status: tripData.trip?.status,
        assignment_location_role: assignmentLocationRole
      });

      return await handleNewTrip(
        client,
        tripData.assignment,
        location,
        truck,
        userId,
        new Date()
      );
    } else {
      // Assignment is not compatible - need auto-assignment
      logDebug('NEW_TRIP_CREATION', `${reason} - assignment incompatible, triggering auto-assignment`, {
        assignment_id: tripData.assignment.id,
        location_name: location.name,
        truck_number: truck.truck_number,
        previous_trip_id: tripData.trip?.id,
        previous_trip_status: tripData.trip?.status,
        assignment_location_role: assignmentLocationRole,
        location_type: location.type,
        reason: 'assignment_location_mismatch'
      });

      // Clear the assignment so auto-assignment logic will be triggered
      tripData.assignment = null;
    }
  }

  // If we reach here, no compatible assignment was found
  // Try auto-assignment creation as fallback
  logDebug('ENHANCED_VALIDATION', 'No compatible assignment found - attempting auto-assignment creation', {
    truck_number: truck.truck_number,
    location_name: location.name,
    location_type: location.type,
    has_terminal_trip: shouldCreateNewTrip && !!tripData.trip,
    terminal_trip_status: tripData.trip?.status
  });

  const autoAssignmentCreator = new AutoAssignmentCreator();

  try {
    // Check if auto-assignment creation is appropriate
    const shouldCreateCheck = await autoAssignmentCreator.shouldCreateAutoAssignment({
      truck,
      location,
      client
    });

    if (shouldCreateCheck.shouldCreate) {
      logDebug('ENHANCED_VALIDATION', 'Creating auto-assignment for terminal trip at unassigned location', {
        truck_number: truck.truck_number,
        location_name: location.name,
        location_type: location.type,
        reason: shouldCreateCheck.reason,
        terminal_trip_id: tripData.trip?.id,
        terminal_trip_status: tripData.trip?.status
      });

      // Create dynamic auto-assignment with progressive route discovery
      const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
        truck,
        location,
        client,
        userId,
        enableDynamicRouting: true // Enable progressive route building
      });

      EnhancedLogger.logAssignmentLookup(truck.id, {
        truck_number: qrData.id,
        location_id: location.id,
        location_name: location.name,
        assignment_found: true,
        assignment_id: autoAssignment.id,
        auto_created: true,
        context: 'terminal_trip_auto_assignment'
      }, [autoAssignment], 'AUTO_ASSIGNMENT_CREATED_TERMINAL_TRIP');

      // Create new trip with auto-assignment
      return await handleNewTrip(
        client,
        autoAssignment,
        location,
        truck,
        userId,
        new Date()
      );
    } else {
      logDebug('ENHANCED_VALIDATION', 'Auto-assignment creation not appropriate', {
        truck_number: truck.truck_number,
        location_name: location.name,
        reason: shouldCreateCheck.reason,
        recommendation: shouldCreateCheck.recommendation
      });

      // Return error with clear message
      throw new Error(`Cannot create assignment for truck ${truck.truck_number} at ${location.name}. ${shouldCreateCheck.reason}. ${shouldCreateCheck.recommendation}`);
    }
  } catch (autoAssignmentError) {
    logError('ENHANCED_VALIDATION', autoAssignmentError, {
      truck_number: truck.truck_number,
      location_name: location.name,
      terminal_trip_id: tripData.trip?.id,
      terminal_trip_status: tripData.trip?.status
    });

    // Return error with clear message
    throw new Error(`Failed to create assignment for truck ${truck.truck_number} at ${location.name}: ${autoAssignmentError.message}`);
  }

  } catch (error) {
    logError('TRUCK_SCAN_ERROR', error, {
      truck_number: qrData.id,
      location_code: locationScanData?.id,
      user_id: userId,
      error_code: error.code,
      error_message: error.message
    });

    // Re-throw the error to be handled by the main transaction handler
    throw error;
  }
}

// FIXED: Query to get current trip and its associated assignment
async function getCurrentTripAndAssignment(client, truckId) {
  // CRITICAL FIX: Find the assignment that has the active trip, not the most recent assignment
  const result = await client.query(`
    WITH current_trip AS (
      SELECT tl.*, tl.assignment_id as trip_assignment_id
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      WHERE a.truck_id = $1
        AND tl.status NOT IN ('trip_completed', 'cancelled', 'stopped')
      ORDER BY tl.created_at DESC
      LIMIT 1
    ),
    trip_assignment AS (
      -- Get the assignment for the active trip (if any)
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.notes,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        ct.trip_assignment_id as active_assignment_id
      FROM current_trip ct
      JOIN assignments a ON ct.trip_assignment_id = a.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
    ),
    fallback_assignment AS (
      -- Fallback: get most recent assignment if no active trip
      SELECT
        a.id as assignment_id,
        a.truck_id,
        a.driver_id,
        a.status as assignment_status,
        a.priority,
        a.expected_loads_per_day,
        a.assigned_date,
        a.start_time,
        a.end_time,
        a.notes,
        a.created_at as assignment_created_at,
        ll.id as loading_location_id,
        ll.name as loading_location_name,
        ul.id as unloading_location_id,
        ul.name as unloading_location_name,
        a.id as active_assignment_id
      FROM assignments a
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.truck_id = $1
        AND a.status IN ('assigned', 'in_progress')
        AND NOT EXISTS (SELECT 1 FROM current_trip)
      ORDER BY a.created_at DESC
      LIMIT 1
    ),
    combined_assignment AS (
      SELECT * FROM trip_assignment
      UNION ALL
      SELECT * FROM fallback_assignment
      LIMIT 1
    )
    SELECT
      ct.id as trip_id,
      ct.trip_assignment_id,
      ct.trip_number,
      ct.status as trip_status,
      ct.loading_start_time,
      ct.loading_end_time,
      ct.unloading_start_time,
      ct.unloading_end_time,
      false as is_exception,
      null as exception_reason,
      ct.actual_loading_location_id,
      ct.actual_unloading_location_id,
      ca.assignment_id,
      ca.truck_id,
      ca.driver_id,
      ca.assignment_status,
      ca.priority,
      ca.expected_loads_per_day,
      ca.loading_location_id,
      ca.loading_location_name,
      ca.unloading_location_id,
      ca.unloading_location_name,
      ca.active_assignment_id
    FROM combined_assignment ca
    LEFT JOIN current_trip ct ON ct.trip_assignment_id = ca.assignment_id
  `, [truckId]);

  if (result.rows.length === 0) {
    return { trip: null, assignment: null };
  }

  const row = result.rows[0];
  
  // Extract trip data if exists
  const trip = row.trip_id ? {
    id: row.trip_id,
    assignment_id: row.trip_assignment_id,
    trip_number: row.trip_number,
    status: row.trip_status,
    loading_start_time: row.loading_start_time,
    loading_end_time: row.loading_end_time,
    unloading_start_time: row.unloading_start_time,
    unloading_end_time: row.unloading_end_time,
    is_exception: false,
    exception_reason: null,
    actual_loading_location_id: row.actual_loading_location_id,
    actual_unloading_location_id: row.actual_unloading_location_id
  } : null;
  // Extract assignment data
  const assignment = {
    id: row.assignment_id,
    truck_id: row.truck_id,
    driver_id: row.driver_id,
    loading_location_id: row.loading_location_id,
    loading_location_name: row.loading_location_name,
    unloading_location_id: row.unloading_location_id,
    unloading_location_name: row.unloading_location_name,
    status: row.assignment_status,
    priority: row.priority,
    expected_loads_per_day: row.expected_loads_per_day,
    assigned_date: row.assigned_date,
    start_time: row.start_time,
    end_time: row.end_time
  };

  return { trip, assignment };
}

// Simplified trip progression logic - pure assignment-based flow
async function determineNextAction(client, currentTrip, assignment, location, truck, userId) {
  const now = new Date();

  // Check if trip exists
  if (!currentTrip) {
    throw new Error('No active trip found. Please start a new trip by scanning at the assigned loading location.');
  }

  // Verify trip state is valid before proceeding (enhanced states with return travel)
  const validStates = ['loading_start', 'loading_end', 'unloading_start', 'unloading_end'];
  if (!validStates.includes(currentTrip.status)) {
    throw new Error(`Invalid trip state: ${currentTrip.status}. Cannot determine next action.`);
  }

  logDebug('TRIP_PROGRESSION', 'Determining next action for trip', {
    trip_id: currentTrip.id,
    current_status: currentTrip.status,
    location_name: location.name,
    location_type: location.type,
    truck_number: truck.truck_number
  });

  // Simplified trip progression based on current status
  switch (currentTrip.status) {

    case 'loading_start':
      // Verify we're at the right location type before allowing loading to start/end
      if (location.type !== 'loading') {
        throw new Error(`Cannot perform loading operation at a ${location.type} location. Must be at a loading location.`);
      }
      return await handleLoadingStart(client, currentTrip, assignment, location, now);
    
    case 'loading_end':
      // Proceed to unloading - allow flexible unloading locations
      return await handleLoadingEnd(client, currentTrip, assignment, location, userId, now);

    case 'unloading_start':
      // Complete unloading at current location (unloading_start → unloading_end)
      return await handleUnloadingStart(client, currentTrip, assignment, location, now);

    case 'unloading_end':
      // Handle return travel and trip completion (unloading_end → trip_completed + new trip)
      return await handleUnloadingEnd(client, currentTrip, assignment, location, truck, userId, now);
    
    default:
      throw new Error(`Invalid trip status: ${currentTrip.status}`);
  }
}

// Enhanced new trip creation with dynamic route discovery support and trip deduplication
async function handleNewTrip(client, assignment, location, truck, userId, now) {
  // CRITICAL FIX: Check for existing TRULY ACTIVE trips by truck_id to prevent duplicates
  // Only consider trips that are truly in progress (not completed or unloading_end)
  const existingTripResult = await client.query(`
    SELECT
      tl.id, tl.trip_number, tl.status, tl.assignment_id,
      tl.loading_start_time, tl.loading_end_time,
      tl.unloading_start_time, tl.unloading_end_time,
      a.assignment_code, a.truck_id,
      dt.truck_number
    FROM trip_logs tl
    JOIN assignments a ON tl.assignment_id = a.id
    JOIN dump_trucks dt ON a.truck_id = dt.id
    WHERE dt.id = $1
      AND tl.status IN ('loading_start', 'loading_end', 'unloading_start')
    ORDER BY tl.created_at DESC
    LIMIT 1
  `, [truck.id]);

  if (existingTripResult.rows.length > 0) {
    const existingTrip = existingTripResult.rows[0];

    // CRITICAL FIX: Check if this is trip progression vs assignment update
    // If truck is scanning at the same location for the same assignment, this is trip progression
    const isTripProgression = (
      existingTrip.assignment_id === assignment.id &&
      ((existingTrip.status === 'loading_start' && location.type === 'loading') ||
       (existingTrip.status === 'loading_end' && location.type === 'unloading') ||
       (existingTrip.status === 'unloading_start' && location.type === 'unloading'))
    );

    if (isTripProgression) {
      logDebug('TRIP_PROGRESSION', 'Found existing trip for progression - calling determineNextAction', {
        truck_id: truck.id,
        truck_number: truck.truck_number,
        existing_trip_id: existingTrip.id,
        trip_status: existingTrip.status,
        location_name: location.name,
        location_type: location.type,
        assignment_id: assignment.id
      });

      // This is trip progression - call determineNextAction
      return await determineNextAction(client, existingTrip, assignment, location, truck, userId);
    } else {
      logDebug('TRIP_DEDUPLICATION', 'Found existing active trip for truck - updating instead of creating duplicate', {
        truck_id: truck.id,
        truck_number: truck.truck_number,
        existing_trip_id: existingTrip.id,
        existing_assignment_id: existingTrip.assignment_id,
        new_assignment_id: assignment.id,
        location_name: location.name,
        location_type: location.type
      });

      // Update the existing trip to use the new assignment and location
      return await updateExistingTripForNewAssignment(
        client,
        existingTrip,
        assignment,
        location,
        truck,
        userId,
        now
      );
    }
  }

  const tripNumber = await getNextTripNumber(client, assignment.id);

  // Check if this is a dynamic assignment
  let isDynamicAssignment = false;
  try {
    const assignmentNotes = JSON.parse(assignment.notes || '{}');
    isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
  } catch (error) {
    // Not a dynamic assignment or invalid notes
    isDynamicAssignment = false;
  }

  // CRITICAL FIX: Determine trip status based on ASSIGNMENT LOCATION ROLE, not just location type
  // The assignment's location_role tells us what operation should happen at this location
  let tripStatus, timeField, actualLocationField, message, nextStep;

  // Get the assignment's location role for this specific location
  let assignmentLocationRole = 'none';
  if (assignment.loading_location_id === location.id) {
    assignmentLocationRole = 'loading';
  } else if (assignment.unloading_location_id === location.id) {
    assignmentLocationRole = 'unloading';
  }

  if (assignmentLocationRole === 'loading' && location.type === 'loading') {
    // Standard loading start - truck at assigned loading location
    tripStatus = 'loading_start';
    timeField = 'loading_start_time';
    actualLocationField = 'actual_loading_location_id';
    message = `Loading started at ${location.name}`;
    nextStep = 'scan_loading_end';

    logDebug('TRIP_CREATION', 'Creating loading trip at assigned loading location', {
      assignment_id: assignment.id,
      location_name: location.name,
      location_type: location.type,
      assignment_location_role: assignmentLocationRole,
      truck_number: truck.truck_number,
      operation: 'loading_start'
    });
  } else if (assignmentLocationRole === 'unloading' && location.type === 'unloading') {
    // Standard unloading start - truck at assigned unloading location
    // This happens when truck completes A→B trip and arrives at Point B
    tripStatus = 'unloading_start';
    timeField = 'unloading_start_time';
    actualLocationField = 'actual_unloading_location_id';
    message = `Unloading started at ${location.name}`;
    nextStep = 'scan_unloading_end';

    logDebug('TRIP_CREATION', 'Creating unloading trip at assigned unloading location', {
      assignment_id: assignment.id,
      location_name: location.name,
      location_type: location.type,
      assignment_location_role: assignmentLocationRole,
      truck_number: truck.truck_number,
      operation: 'unloading_start'
    });
  } else if (location.type === 'unloading' && isDynamicAssignment) {
    // Dynamic route discovery: truck scanned at unloading location first
    tripStatus = 'unloading_start';
    timeField = 'unloading_start_time';
    actualLocationField = 'actual_unloading_location_id';
    message = `Dynamic route discovered: Unloading started at ${location.name}`;
    nextStep = 'scan_unloading_end';

    logDebug('DYNAMIC_ROUTE_DISCOVERY', 'Creating trip for dynamic route discovery', {
      assignment_id: assignment.id,
      location_name: location.name,
      location_type: location.type,
      assignment_location_role: assignmentLocationRole,
      truck_number: truck.truck_number,
      discovery_mode: 'unloading_first'
    });
  } else {
    // CRITICAL VALIDATION: Reject invalid combinations
    const errorMessage = `Invalid operation: Cannot create trip at ${location.name}. Assignment role: ${assignmentLocationRole}, Location type: ${location.type}. Valid combinations: loading→loading, unloading→unloading, or dynamic assignments.`;

    logError('ASSIGNMENT_LOCATION_ROLE_MISMATCH', new Error(errorMessage), {
      location_name: location.name,
      location_type: location.type,
      location_id: location.id,
      assignment_location_role: assignmentLocationRole,
      assignment_loading_location_id: assignment.loading_location_id,
      assignment_unloading_location_id: assignment.unloading_location_id,
      truck_number: truck.truck_number,
      assignment_id: assignment.id,
      is_dynamic_assignment: isDynamicAssignment
    });

    throw new Error(errorMessage);
  }

  // Capture active driver information for historical tracking
  const activeDriver = await captureActiveDriverInfo(client, truck.id, now);

  // Create trip with appropriate status and fields including driver history
  const insertQuery = `
    INSERT INTO trip_logs (
      assignment_id, trip_number, status, ${timeField},
      ${actualLocationField},
      performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
      performed_by_shift_id, performed_by_shift_type,
      created_at, updated_at
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
    RETURNING *
  `;

  const newTripResult = await client.query(insertQuery, [
    assignment.id, tripNumber, tripStatus, now, location.id,
    activeDriver?.driver_id || null,
    activeDriver?.driver_name || null,
    activeDriver?.employee_id || null,
    activeDriver?.shift_id || null,
    activeDriver?.shift_type || null,
    now, now
  ]);

  const newTrip = newTripResult.rows[0];

  logDebug('NEW_TRIP', 'Created new trip with driver history', {
    trip_id: newTrip.id,
    trip_number: tripNumber,
    assignment_id: assignment.id,
    location_name: location.name,
    location_type: location.type,
    trip_status: tripStatus,
    is_dynamic: isDynamicAssignment,
    truck_number: truck.truck_number,
    performed_by_driver_id: newTrip.performed_by_driver_id,
    performed_by_driver_name: newTrip.performed_by_driver_name,
    performed_by_shift_type: newTrip.performed_by_shift_type
  });

  // Send notification for trip start
  try {
    notifyTripStatusChanged({
      id: newTrip.id,
      trip_number: tripNumber,
      status: tripStatus,
      truck_number: truck.truck_number,
      location_name: location.name,
      is_dynamic_route: isDynamicAssignment
    });
  } catch (notifyError) {
    console.error('Failed to send trip notification:', notifyError);
  }

  return {
    message,
    trip_log_id: newTrip.id,
    trip_data: newTrip,
    next_step: nextStep
  };
}

// SIMPLIFIED: Enhanced assignment validation handles all scenarios without exceptions

// Handle loading completion (loading_start → loading_end)
async function handleLoadingStart(client, trip, assignment, location, now) {
  // CRITICAL FIX: This function handles loading completion, not loading start
  // When trip is in 'loading_start' status, scanning truck QR should complete loading

  // Check if at the correct loading location for completion
  const expectedLocationId = trip.actual_loading_location_id || assignment.loading_location_id;

  if (location.id !== expectedLocationId) {
    throw new Error(`Cannot complete loading at ${location.name}. Must complete loading at the same location where it started (expected location ID: ${expectedLocationId})`);
  }

  // Calculate loading duration
  const loadingDuration = Math.round(
    (now - new Date(trip.loading_start_time)) / (1000 * 60)
  );

  // Update trip to loading_end
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, loading_end_time = $2, loading_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['loading_end', now, loadingDuration, now, trip.id]);

  return {
    message: `Loading completed in ${loadingDuration} minutes. Proceed to unloading location.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'travel_to_unloading'
  };
}

// Handle loading end state
async function handleLoadingEnd(client, trip, assignment, location, userId, now) {
  if (location.id !== assignment.unloading_location_id) {
    if (location.type === 'unloading') {
      // ENHANCED TRIP FLOW LOGIC: Unassigned unloading location with smart assignment handling
      // DECISION: UPDATE existing assignment vs CREATE new assignment

      logDebug('UNASSIGNED_UNLOADING', 'Processing unassigned unloading location - analyzing assignment strategy', {
        trip_id: trip.id,
        assignment_id: assignment.id,
        assigned_unloading: assignment.unloading_location_id,
        actual_unloading: location.id,
        location_name: location.name,
        truck_id: assignment.truck_id,
        trip_status: trip.status
      });

      // STEP 1: SMART ASSIGNMENT STRATEGY DECISION
      // Analyze whether to UPDATE current assignment or CREATE new assignment

      const shouldUpdateCurrentAssignment = await analyzeAssignmentUpdateStrategy(client, assignment, location, trip);

      if (shouldUpdateCurrentAssignment.update) {
        // STRATEGY A: UPDATE CURRENT ASSIGNMENT (Recommended for mid-trip changes)
        logDebug('ASSIGNMENT_UPDATE_STRATEGY', 'Updating current assignment with new unloading location', {
          assignment_id: assignment.id,
          assignment_code: assignment.assignment_code,
          old_unloading: assignment.unloading_location_id,
          new_unloading: location.id,
          reason: shouldUpdateCurrentAssignment.reason
        });

        // Update current assignment with new unloading location
        const updatedAssignment = await client.query(`
          UPDATE assignments
          SET unloading_location_id = $1,
              updated_at = $2,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb
          WHERE id = $4
          RETURNING *
        `, [
          location.id,
          now,
          JSON.stringify({
            destination_change: {
              timestamp: now,
              previous_unloading_location_id: assignment.unloading_location_id,
              new_unloading_location_id: location.id,
              change_reason: 'mid_trip_destination_change',
              trip_id: trip.id,
              changed_by_user_id: userId
            }
          }),
          assignment.id
        ]);

        logDebug('ASSIGNMENT_UPDATED', 'Current assignment updated with new destination', {
          assignment_id: assignment.id,
          assignment_code: assignment.assignment_code,
          new_route: `${assignment.loading_location_name} → ${location.name}`,
          change_type: 'destination_update'
        });

        // Update the assignment object for subsequent operations
        assignment.unloading_location_id = location.id;
        assignment.unloading_location_name = location.name;
      }

      // STEP 2: Auto-create assignment for future trips (regardless of update strategy)
      // This ensures the new unloading location is included in future standard flows
      let autoAssignment = null;
      try {
        const autoAssignmentCreator = new AutoAssignmentCreator();

        // Get truck information for auto-assignment creation
        const truckResult = await client.query(`
          SELECT dt.* FROM dump_trucks dt WHERE dt.id = $1
        `, [assignment.truck_id]);

        if (truckResult.rows.length > 0) {
          const truck = truckResult.rows[0];

          logDebug('AUTO_ASSIGNMENT_UNLOADING', 'Creating auto-assignment for unassigned unloading location', {
            truck_number: truck.truck_number,
            location_name: location.name,
            location_type: location.type,
            trigger: 'unassigned_unloading_scan'
          });

          // Create auto-assignment that includes this new unloading location
          // ENHANCED: Pass current trip context to ensure correct loading location is used
          autoAssignment = await autoAssignmentCreator.createAutoAssignment({
            truck,
            location,
            client,
            userId,
            enableDynamicRouting: true, // Enable progressive route discovery
            currentTripContext: {
              trip_id: trip.id,
              actual_loading_location_id: trip.actual_loading_location_id,
              assignment_id: trip.assignment_id,
              status: trip.status
            }
          });

          logDebug('AUTO_ASSIGNMENT_CREATED_UNLOADING', 'Auto-assignment created for unassigned unloading location', {
            assignment_id: autoAssignment.id,
            assignment_code: autoAssignment.assignment_code,
            route: `${autoAssignment.loading_location_name} → ${autoAssignment.unloading_location_name}`,
            creation_method: 'unassigned_unloading_trigger'
          });
        }
      } catch (autoAssignmentError) {
        // Log error but don't fail the trip update
        logError('AUTO_ASSIGNMENT_UNLOADING_ERROR', autoAssignmentError, {
          trip_id: trip.id,
          location_id: location.id,
          location_name: location.name
        });
        console.warn(`Failed to create auto-assignment for unassigned unloading location: ${autoAssignmentError.message}`);
      }

      // STEP 2: THEN update current trip with unassigned unloading location
      const updatedTrip = await client.query(`
        UPDATE trip_logs
        SET status = $1, unloading_start_time = $2, actual_unloading_location_id = $3, updated_at = $4
        WHERE id = $5
        RETURNING *
      `, [
        'unloading_start',
        now,
        location.id,
        now,
        trip.id
      ]);

      logDebug('TRIP_UPDATED_UNLOADING', 'Updated current trip with unassigned unloading location', {
        trip_id: trip.id,
        new_status: 'unloading_start',
        actual_unloading_location_id: location.id,
        auto_assignment_created: autoAssignment ? autoAssignment.id : 'failed'
      });

      // Send notification for trip status change
      try {
        notifyTripStatusChanged({
          id: trip.id,
          trip_number: trip.trip_number,
          status: 'unloading_start',
          truck_number: assignment.truck_number || 'Unknown',
          location_name: location.name,
          message: `Trip unloading started at ${location.name}`,
          data: {
            truck: {
              id: assignment.truck_id,
              number: assignment.truck_number || 'Unknown'
            },
            location: {
              id: location.id,
              name: location.name,
              type: location.type
            },
            assignment: {
              id: assignment.id,
              assignment_code: assignment.assignment_code
            }
          }
        });
      } catch (notifyError) {
        console.error('Failed to send trip status notification:', notifyError);
      }

      return {
        message: `Route deviation: Unloading at ${location.name}. Auto-assignment created for future trips.`,
        trip_log_id: trip.id,
        trip_data: updatedTrip.rows[0],
        next_step: 'scan_unloading_end'
      };
    } else {
      throw new Error(`Expected unloading location, but scanned ${location.type} location`);
    }
  }

  // Calculate travel duration
  const travelDuration = Math.round(
    (now - new Date(trip.loading_end_time)) / (1000 * 60)
  );

  // Normal unloading start
  const updatedTrip = await client.query(`
    UPDATE trip_logs 
    SET status = $1, unloading_start_time = $2, travel_duration_minutes = $3, updated_at = $4
    WHERE id = $5
    RETURNING *
  `, ['unloading_start', now, travelDuration, now, trip.id]);

  return {
    message: `Arrived at unloading location after ${travelDuration} minutes travel. Start unloading.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'scan_unloading_end'
  };
}

// Handle unloading start state (unloading_start → unloading_end)
async function handleUnloadingStart(client, trip, assignment, location, now) {
  const expectedLocationId = trip.actual_unloading_location_id || assignment.unloading_location_id;

  if (parseInt(location.id) !== parseInt(expectedLocationId)) {
    throw new Error('Must complete unloading at the same location where it started');
  }

  // Calculate unloading duration
  const unloadingDuration = Math.round(
    (now - new Date(trip.unloading_start_time)) / (1000 * 60)
  );

  // ENHANCED WORKFLOW: Move to unloading_end instead of trip_completed
  // This allows for return travel calculation when truck scans at loading location
  const updatedTrip = await client.query(`
    UPDATE trip_logs
    SET status = $1,
        unloading_end_time = $2,
        unloading_duration_minutes = $3,
        updated_at = $2
    WHERE id = $4
    RETURNING *
  `, ['unloading_end', now, unloadingDuration, trip.id]);

  logDebug('UNLOADING_COMPLETED', 'Unloading completed - ready for return travel', {
    trip_id: trip.id,
    unloading_duration: unloadingDuration,
    workflow: 'loading_start → loading_end → unloading_start → unloading_end'
  });

  return {
    message: `Unloading completed in ${unloadingDuration} minutes. Return to loading location to complete trip.`,
    trip_log_id: trip.id,
    trip_data: updatedTrip.rows[0],
    next_step: 'return_to_loading'
  };
}

// Handle stopped trips - create new trips WITHOUT modifying stopped status
async function handleStoppedNewTrip(client, trip, assignment, location, truck, userId, now) {
  logDebug('STOPPED_NEW_TRIP', 'Creating new trip after stop without modifying stopped status', {
    stopped_trip_id: trip.id,
    stopped_status: trip.status,
    location_name: location.name,
    truck_number: truck.truck_number
  });

  // ENHANCED: Support both loading locations AND unassigned locations for return travel
  if (location.type !== 'loading') {
    // Handle unassigned locations that could become loading locations
    logDebug('STOPPED_UNASSIGNED_LOCATION', 'Truck scanned at unassigned location after stop', {
      trip_id: trip.id,
      location_name: location.name,
      location_type: location.type,
      truck_id: truck.id
    });

    // For unassigned locations, attempt to create a dynamic assignment
    try {
      const autoAssignmentCreator = new AutoAssignmentCreator();
      const autoAssignmentResult = await autoAssignmentCreator.createAutoAssignment({
        truck: truck,
        location: location,
        client: client,
        userId: userId,
        enableDynamicRouting: true,
        independentAssignment: true,
        context: 'stopped_unassigned_location'
      });

      if (autoAssignmentResult && autoAssignmentResult.success) {
        logDebug('STOPPED_UNASSIGNED_SUCCESS', 'Auto-assignment created for unassigned stopped location', {
          trip_id: trip.id,
          location_name: location.name,
          new_assignment_id: autoAssignmentResult.assignment.id
        });

        // Update location type to loading for processing
        location.type = 'loading';
      } else {
        throw new Error(`Cannot create new trip at unassigned location: ${location.name}. Auto-assignment failed: ${autoAssignmentResult?.error || 'Unknown error'}`);
      }
    } catch (unassignedError) {
      logError('STOPPED_UNASSIGNED_ERROR', unassignedError, {
        trip_id: trip.id,
        location_name: location.name,
        truck_id: truck.id
      });
      throw new Error(`Cannot create new trip at unassigned location: ${location.name}. ${unassignedError.message}`);
    }
  }

  // Calculate stopped duration (stopped_reported_at to now)
  let stoppedDuration = 0;
  if (trip.stopped_reported_at) {
    stoppedDuration = Math.round(
      (now - new Date(trip.stopped_reported_at)) / (1000 * 60)
    );
  }

  // CRITICAL: Do NOT update the stopped trip status - preserve it for audit
  // The stopped trip remains as "stopped" status forever

  // ENHANCED: Dynamic return destination handling
  // First, try to find existing assignment for this specific location
  const existingAssignmentQuery = `
    SELECT a.*, ll.name as loading_location_name, ul.name as unloading_location_name
    FROM assignments a
    LEFT JOIN locations ll ON a.loading_location_id = ll.id
    LEFT JOIN locations ul ON a.unloading_location_id = ul.id
    WHERE a.truck_id = $1
      AND a.loading_location_id = $2
      AND a.status IN ('assigned', 'in_progress')
    ORDER BY a.created_at DESC
    LIMIT 1
  `;

  let newAssignmentResult = await client.query(existingAssignmentQuery, [truck.id, location.id]);
  let newAssignment = null;
  let isAutoCreatedAssignment = false;

  if (newAssignmentResult.rows.length > 0) {
    // Found existing assignment for this location
    newAssignment = newAssignmentResult.rows[0];
    logDebug('STOPPED_EXISTING_ASSIGNMENT', 'Found existing assignment for stopped recovery location', {
      trip_id: trip.id,
      assignment_id: newAssignment.id,
      location_name: location.name,
      assignment_type: 'existing'
    });
  } else {
    // No existing assignment - check if we should auto-create one
    logDebug('STOPPED_NO_ASSIGNMENT', 'No existing assignment found for stopped recovery location', {
      trip_id: trip.id,
      location_name: location.name,
      location_type: location.type,
      truck_id: truck.id
    });

    // DYNAMIC ROUTE DISCOVERY: Auto-create assignment for unassigned stopped recovery location
    if (location.type === 'loading') {
      try {
        const autoAssignmentCreator = new AutoAssignmentCreator();
        const autoAssignmentResult = await autoAssignmentCreator.createAutoAssignment({
          truck: truck,
          location: location,
          client: client,
          userId: userId,
          enableDynamicRouting: true, // Enable dynamic routing for stopped recovery
          independentAssignment: true,
          context: 'stopped_recovery_auto_assignment'
        });

        if (autoAssignmentResult && autoAssignmentResult.success) {
          newAssignment = autoAssignmentResult.assignment;
          isAutoCreatedAssignment = true;

          logDebug('STOPPED_AUTO_ASSIGNMENT_SUCCESS', 'Auto-created assignment for stopped recovery location', {
            trip_id: trip.id,
            new_assignment_id: newAssignment.id,
            assignment_code: newAssignment.assignment_code,
            location_name: location.name,
            assignment_type: 'auto_created'
          });
        } else {
          logDebug('STOPPED_AUTO_ASSIGNMENT_FAILED', 'Failed to auto-create assignment for stopped recovery location', {
            trip_id: trip.id,
            location_name: location.name,
            error: autoAssignmentResult?.error || 'Unknown error'
          });
        }
      } catch (autoAssignmentError) {
        logError('STOPPED_AUTO_ASSIGNMENT_ERROR', autoAssignmentError, {
          trip_id: trip.id,
          location_name: location.name,
          truck_id: truck.id
        });
      }
    }
  }

  if (newAssignment) {
    // Start new trip immediately (either existing or auto-created assignment)
    const newTripNumber = await getNextTripNumber(client, newAssignment.id);

    // Capture active driver for the new trip
    const activeDriver = await captureActiveDriverInfo(client, truck.id, now);

    const newTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, loading_start_time,
        actual_loading_location_id,
        performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
        performed_by_shift_id, performed_by_shift_type,
        created_at, updated_at, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `, [
      newAssignment.id,
      newTripNumber,
      'loading_start',
      now,
      location.id,
      activeDriver?.driver_id || null,
      activeDriver?.driver_name || null,
      activeDriver?.employee_id || null,
      activeDriver?.shift_id || null,
      activeDriver?.shift_type || null,
      now,
      now,
      JSON.stringify({
        workflow_type: 'trip_recovery',
        previous_stopped_trip_id: trip.id,
        stopped_duration_minutes: stoppedDuration,
        assignment_type: isAutoCreatedAssignment ? 'auto_created' : 'existing',
        recovery_location_type: isAutoCreatedAssignment ? 'unconfirmed_loading_location' : 'confirmed_loading_location'
      })
    ]);

    logDebug('STOPPED_NEW_TRIP_CREATED', 'New trip created after trip recovery', {
      stopped_trip_id: trip.id,
      new_trip_id: newTrip.rows[0].id,
      new_assignment_id: newAssignment.id,
      location_name: location.name,
      stopped_duration: stoppedDuration,
      assignment_type: isAutoCreatedAssignment ? 'auto_created' : 'existing'
    });

    // Enhanced message based on assignment type
    let message;
    if (isAutoCreatedAssignment) {
      message = `Trip recovery! New trip started at Unconfirmed Loading Location: ${location.name} ❓. Stopped duration: ${stoppedDuration} minutes.`;
    } else {
      message = `Trip recovery! New trip started at ${location.name} 📍. Stopped duration: ${stoppedDuration} minutes.`;
    }

    return {
      message: message,
      trip_log_id: newTrip.rows[0].id,
      trip_data: newTrip.rows[0],
      stopped_trip_data: trip, // Include stopped trip for reference
      stopped_duration: stoppedDuration,
      assignment_type: isAutoCreatedAssignment ? 'auto_created' : 'existing',
      location_certainty: isAutoCreatedAssignment ? 'unconfirmed' : 'confirmed',
      next_step: 'scan_loading_end'
    };
  } else {
    // No assignment could be found or created
    throw new Error(`Cannot create new trip after stopped at ${location.name}. No suitable assignment available.`);
  }
}

// Enhanced handle unloading end state with dynamic return destination support
async function handleUnloadingEnd(client, trip, assignment, location, truck, userId, now) {
  // ENHANCED: Support both loading locations AND unassigned locations for return travel
  if (location.type !== 'loading') {
    // Handle unassigned locations that could become loading locations
    logDebug('RETURN_TRAVEL_UNASSIGNED_LOCATION', 'Truck scanned at unassigned location during return travel', {
      trip_id: trip.id,
      location_name: location.name,
      location_type: location.type,
      truck_id: truck.id
    });

    // For unassigned locations, attempt to create a dynamic assignment
    try {
      const autoAssignmentCreator = new AutoAssignmentCreator();
      const autoAssignmentResult = await autoAssignmentCreator.createAutoAssignment({
        truck: truck,
        location: location,
        client: client,
        userId: userId,
        enableDynamicRouting: true,
        independentAssignment: true,
        context: 'return_travel_unassigned_location'
      });

      if (autoAssignmentResult && autoAssignmentResult.success) {
        logDebug('RETURN_TRAVEL_UNASSIGNED_SUCCESS', 'Auto-assignment created for unassigned return location', {
          trip_id: trip.id,
          location_name: location.name,
          new_assignment_id: autoAssignmentResult.assignment.id
        });

        // Update location type to loading for processing
        location.type = 'loading';
      } else {
        throw new Error(`Cannot complete return travel at unassigned location: ${location.name}. Auto-assignment failed: ${autoAssignmentResult?.error || 'Unknown error'}`);
      }
    } catch (unassignedError) {
      logError('RETURN_TRAVEL_UNASSIGNED_ERROR', unassignedError, {
        trip_id: trip.id,
        location_name: location.name,
        truck_id: truck.id
      });
      throw new Error(`Cannot complete return travel at unassigned location: ${location.name}. ${unassignedError.message}`);
    }
  }

  // Calculate return travel duration (unloading_end to loading location)
  const returnTravelDuration = Math.round(
    (now - new Date(trip.unloading_end_time)) / (1000 * 60)
  );

  // Calculate total trip duration (loading_start to now)
  const totalDuration = Math.round(
    (now - new Date(trip.loading_start_time)) / (1000 * 60)
  );

  // Calculate forward travel duration (loading_end to unloading_start) if not already set
  let forwardTravelDuration = trip.travel_duration_minutes;
  if (!forwardTravelDuration && trip.loading_end_time && trip.unloading_start_time) {
    forwardTravelDuration = Math.round(
      (new Date(trip.unloading_start_time) - new Date(trip.loading_end_time)) / (1000 * 60)
    );
  }

  // Complete the current trip
  const completedTrip = await client.query(`
    UPDATE trip_logs
    SET status = $1,
        trip_completed_time = $2,
        total_duration_minutes = $3,
        travel_duration_minutes = $4,
        updated_at = $2,
        notes = COALESCE(notes::jsonb, '{}'::jsonb) || $5::jsonb
    WHERE id = $6
    RETURNING *
  `, [
    'trip_completed',
    now,
    totalDuration,
    forwardTravelDuration,
    JSON.stringify({
      return_travel_duration_minutes: returnTravelDuration,
      return_travel_start: trip.unloading_end_time,
      return_travel_end: now.toISOString(),
      workflow_type: 'enhanced_with_return_travel'
    }),
    trip.id
  ]);

  logDebug('TRIP_COMPLETED_WITH_RETURN', 'Trip completed with return travel calculation', {
    trip_id: trip.id,
    loading_duration: trip.loading_duration_minutes,
    forward_travel_duration: forwardTravelDuration,
    unloading_duration: trip.unloading_duration_minutes,
    return_travel_duration: returnTravelDuration,
    total_duration: totalDuration
  });

  // ENHANCED: Dynamic return destination handling
  // First, try to find existing assignment for this specific location
  const existingAssignmentQuery = `
    SELECT a.*, ll.name as loading_location_name, ul.name as unloading_location_name
    FROM assignments a
    LEFT JOIN locations ll ON a.loading_location_id = ll.id
    LEFT JOIN locations ul ON a.unloading_location_id = ul.id
    WHERE a.truck_id = $1
      AND a.loading_location_id = $2
      AND a.status IN ('assigned', 'in_progress')
    ORDER BY a.created_at DESC
    LIMIT 1
  `;

  let newAssignmentResult = await client.query(existingAssignmentQuery, [truck.id, location.id]);
  let newAssignment = null;
  let isAutoCreatedAssignment = false;

  if (newAssignmentResult.rows.length > 0) {
    // Found existing assignment for this location
    newAssignment = newAssignmentResult.rows[0];
    logDebug('RETURN_TRAVEL_EXISTING_ASSIGNMENT', 'Found existing assignment for return destination', {
      trip_id: trip.id,
      assignment_id: newAssignment.id,
      location_name: location.name,
      assignment_type: 'existing'
    });
  } else {
    // No existing assignment - check if we should auto-create one
    logDebug('RETURN_TRAVEL_NO_ASSIGNMENT', 'No existing assignment found for return destination', {
      trip_id: trip.id,
      location_name: location.name,
      location_type: location.type,
      truck_id: truck.id
    });

    // DYNAMIC ROUTE DISCOVERY: Auto-create assignment for unassigned return destination
    if (location.type === 'loading') {
      try {
        const autoAssignmentCreator = new AutoAssignmentCreator();
        const autoAssignmentResult = await autoAssignmentCreator.createAutoAssignment({
          truck: truck,
          location: location,
          client: client,
          userId: userId,
          enableDynamicRouting: true, // Enable dynamic routing for return travel
          independentAssignment: true,
          context: 'return_travel_auto_assignment'
        });

        if (autoAssignmentResult && autoAssignmentResult.success) {
          newAssignment = autoAssignmentResult.assignment;
          isAutoCreatedAssignment = true;

          logDebug('RETURN_TRAVEL_AUTO_ASSIGNMENT_SUCCESS', 'Auto-created assignment for return destination', {
            trip_id: trip.id,
            new_assignment_id: newAssignment.id,
            assignment_code: newAssignment.assignment_code,
            location_name: location.name,
            assignment_type: 'auto_created'
          });
        } else {
          logDebug('RETURN_TRAVEL_AUTO_ASSIGNMENT_FAILED', 'Failed to auto-create assignment for return destination', {
            trip_id: trip.id,
            location_name: location.name,
            error: autoAssignmentResult?.error || 'Unknown error'
          });
        }
      } catch (autoAssignmentError) {
        logError('RETURN_TRAVEL_AUTO_ASSIGNMENT_ERROR', autoAssignmentError, {
          trip_id: trip.id,
          location_name: location.name,
          truck_id: truck.id
        });
      }
    }
  }

  if (newAssignment) {
    // Start new trip immediately (either existing or auto-created assignment)
    const newTripNumber = await getNextTripNumber(client, newAssignment.id);

    // Capture active driver for the new trip
    const activeDriver = await captureActiveDriverInfo(client, truck.id, now);

    const newTrip = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, loading_start_time,
        actual_loading_location_id,
        performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
        performed_by_shift_id, performed_by_shift_type,
        created_at, updated_at, notes
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
      RETURNING *
    `, [
      newAssignment.id,
      newTripNumber,
      'loading_start',
      now,
      location.id,
      activeDriver?.driver_id || null,
      activeDriver?.driver_name || null,
      activeDriver?.employee_id || null,
      activeDriver?.shift_id || null,
      activeDriver?.shift_type || null,
      now,
      now,
      JSON.stringify({
        workflow_type: 'enhanced_continuous_cycle',
        previous_trip_id: trip.id,
        auto_started_after_return: true,
        assignment_type: isAutoCreatedAssignment ? 'auto_created' : 'existing',
        return_destination_type: isAutoCreatedAssignment ? 'unconfirmed_loading_location' : 'confirmed_loading_location'
      })
    ]);

    logDebug('NEW_TRIP_AUTO_STARTED', 'New trip auto-started after return travel', {
      previous_trip_id: trip.id,
      new_trip_id: newTrip.rows[0].id,
      new_assignment_id: newAssignment.id,
      location_name: location.name,
      return_travel_duration: returnTravelDuration,
      assignment_type: isAutoCreatedAssignment ? 'auto_created' : 'existing',
      return_destination_type: isAutoCreatedAssignment ? 'unconfirmed' : 'confirmed'
    });

    // Update location sequence for return travel
    try {
      await updateLocationSequence(client, completedTrip.rows[0], assignment, location, 'return_travel_completed');
      logDebug('RETURN_TRAVEL_LOCATION_SEQUENCE_UPDATED', 'Location sequence updated for return travel', {
        trip_id: trip.id,
        new_trip_id: newTrip.rows[0].id,
        location_name: location.name
      });
    } catch (locationSequenceError) {
      logError('RETURN_TRAVEL_LOCATION_SEQUENCE_ERROR', locationSequenceError, {
        trip_id: trip.id,
        new_trip_id: newTrip.rows[0].id,
        location_name: location.name
      });
    }

    // Send WebSocket notification for return travel completion
    try {
      notifyTripStatusChanged({
        id: newTrip.rows[0].id,
        truck_number: truck.truck_number,
        status: 'loading_start',
        location_name: location.name,
        return_travel_duration: returnTravelDuration,
        assignment_type: isAutoCreatedAssignment ? 'auto_created' : 'existing',
        location_certainty: isAutoCreatedAssignment ? 'unconfirmed' : 'confirmed',
        workflow_type: 'return_travel_completed',
        previous_trip_id: trip.id
      });
    } catch (notifyError) {
      logError('RETURN_TRAVEL_NOTIFICATION_ERROR', notifyError, {
        trip_id: newTrip.rows[0].id,
        previous_trip_id: trip.id
      });
    }

    // Enhanced message based on assignment type
    let message;
    if (isAutoCreatedAssignment) {
      message = `Trip completed! Return travel: ${returnTravelDuration} minutes. New trip started at Unconfirmed Loading Location: ${location.name} ❓`;
    } else {
      message = `Trip completed! Return travel: ${returnTravelDuration} minutes. New trip started at ${location.name} 📍`;
    }

    return {
      message: message,
      trip_log_id: newTrip.rows[0].id,
      trip_data: newTrip.rows[0],
      completed_trip_data: completedTrip.rows[0],
      return_travel_duration: returnTravelDuration,
      assignment_type: isAutoCreatedAssignment ? 'auto_created' : 'existing',
      location_certainty: isAutoCreatedAssignment ? 'unconfirmed' : 'confirmed',
      next_step: 'scan_loading_end'
    };
  } else {
    // ENHANCED UX: Instead of just completing trip, attempt auto-assignment creation
    logDebug('RETURN_TRAVEL_AUTO_ASSIGNMENT_ATTEMPT', 'Attempting auto-assignment creation for unassigned location', {
      trip_id: trip.id,
      location_name: location.name,
      location_type: location.type,
      return_travel_duration: returnTravelDuration,
      reason: 'no_existing_assignment_found'
    });

    // Try to create auto-assignment for the unassigned location
    try {
      const autoAssignmentCreator = new AutoAssignmentCreator();
      const autoAssignmentResult = await autoAssignmentCreator.createAutoAssignment({
        truck: truck,
        location: location,
        client: client,
        userId: userId,
        enableDynamicRouting: true,
        independentAssignment: true,
        context: 'return_travel_unassigned_location'
      });

      if (autoAssignmentResult && autoAssignmentResult.success) {
        const newAssignment = autoAssignmentResult.assignment;

        logDebug('RETURN_TRAVEL_AUTO_ASSIGNMENT_SUCCESS', 'Auto-assignment created successfully', {
          trip_id: trip.id,
          new_assignment_id: newAssignment.id,
          assignment_code: newAssignment.assignment_code,
          location_name: location.name
        });

        // Start new trip immediately with the auto-created assignment
        const newTripNumber = await getNextTripNumber(client, newAssignment.id);

        // Capture active driver for the new trip
        const activeDriver = await captureActiveDriverInfo(client, truck.id, now);

        const newTrip = await client.query(`
          INSERT INTO trip_logs (
            assignment_id, trip_number, status, loading_start_time,
            actual_loading_location_id,
            performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
            performed_by_shift_id, performed_by_shift_type,
            created_at, updated_at, notes
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
          RETURNING *
        `, [
          newAssignment.id,
          newTripNumber,
          'loading_start',
          now,
          location.id,
          activeDriver?.driver_id || null,
          activeDriver?.driver_name || null,
          activeDriver?.employee_id || null,
          activeDriver?.shift_id || null,
          activeDriver?.shift_type || null,
          now,
          now,
          JSON.stringify({
            workflow_type: 'enhanced_continuous_cycle',
            previous_trip_id: trip.id,
            auto_started_after_return: true,
            assignment_type: 'auto_created',
            return_destination_type: 'unconfirmed_loading_location'
          })
        ]);

        logDebug('RETURN_TRAVEL_NEW_TRIP_CREATED', 'New trip created with auto-assignment', {
          previous_trip_id: trip.id,
          new_trip_id: newTrip.rows[0].id,
          new_assignment_id: newAssignment.id,
          location_name: location.name,
          return_travel_duration: returnTravelDuration
        });

        // Enhanced message for successful auto-assignment + trip creation
        return {
          message: `Trip completed! Return travel: ${returnTravelDuration} minutes. New trip started at Unconfirmed Loading Location: ${location.name} ❓`,
          trip_log_id: newTrip.rows[0].id,
          trip_data: newTrip.rows[0],
          completed_trip_data: completedTrip.rows[0],
          return_travel_duration: returnTravelDuration,
          assignment_type: 'auto_created',
          location_certainty: 'unconfirmed',
          next_step: 'scan_loading_end'
        };

      } else {
        throw new Error(`Auto-assignment creation failed: ${autoAssignmentResult?.error || 'Unknown error'}`);
      }

    } catch (autoAssignmentError) {
      logError('RETURN_TRAVEL_AUTO_ASSIGNMENT_ERROR', autoAssignmentError, {
        trip_id: trip.id,
        location_name: location.name,
        truck_id: truck.id,
        error_message: autoAssignmentError.message
      });

      // Fallback: Complete trip without new trip (original behavior)
      logDebug('RETURN_TRAVEL_FALLBACK', 'Falling back to trip completion without new trip', {
        trip_id: trip.id,
        location_name: location.name,
        return_travel_duration: returnTravelDuration,
        reason: 'auto_assignment_failed'
      });

      return {
        message: `Trip completed! Return travel: ${returnTravelDuration} minutes. Could not create assignment for ${location.name}. Total trip time: ${totalDuration} minutes.`,
        trip_log_id: trip.id,
        trip_data: completedTrip.rows[0],
        return_travel_duration: returnTravelDuration,
        location_certainty: 'unassigned',
        next_step: 'trip_complete'
      };
    }
  }
}

// Analyze whether to update current assignment or create new assignment for unloading location changes
async function analyzeAssignmentUpdateStrategy(client, assignment, newUnloadingLocation, trip) {
  try {
    // STRATEGY DECISION MATRIX for C→B→D scenario

    // Factor 1: Trip progression stage
    const tripInProgress = ['loading_start', 'loading_end'].includes(trip.status);

    // Factor 2: Assignment age (recent assignments are more likely to be updated)
    const assignmentAge = Date.now() - new Date(assignment.created_at).getTime();
    const isRecentAssignment = assignmentAge < (24 * 60 * 60 * 1000); // Less than 24 hours

    // Factor 3: Check if this is a dynamic/auto-created assignment
    const assignmentNotes = assignment.notes ? JSON.parse(assignment.notes) : {};
    const isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment' ||
                               assignmentNotes.creation_method === 'abc_workflow';

    // Factor 4: Check for existing trips using this assignment
    const existingTripsResult = await client.query(`
      SELECT COUNT(*) as trip_count
      FROM trip_logs
      WHERE assignment_id = $1 AND status = 'trip_completed'
    `, [assignment.id]);
    const hasCompletedTrips = parseInt(existingTripsResult.rows[0].trip_count) > 0;

    // DECISION LOGIC: UPDATE vs CREATE NEW
    if (tripInProgress && isRecentAssignment && isDynamicAssignment && !hasCompletedTrips) {
      // RECOMMENDED: Update current assignment for mid-trip destination changes
      return {
        update: true,
        reason: 'mid_trip_destination_change_on_dynamic_assignment',
        strategy: 'update_current_assignment',
        confidence: 'high'
      };
    } else if (tripInProgress && isRecentAssignment) {
      // ACCEPTABLE: Update recent assignments during active trips
      return {
        update: true,
        reason: 'mid_trip_destination_change_on_recent_assignment',
        strategy: 'update_current_assignment',
        confidence: 'medium'
      };
    } else {
      // FALLBACK: Create new assignment for older or heavily-used assignments
      return {
        update: false,
        reason: 'assignment_too_established_for_modification',
        strategy: 'create_new_assignment',
        confidence: 'medium'
      };
    }

  } catch (error) {
    // SAFE FALLBACK: Default to creating new assignment if analysis fails
    return {
      update: false,
      reason: 'analysis_error_fallback_to_new_assignment',
      strategy: 'create_new_assignment',
      confidence: 'low',
      error: error.message
    };
  }
}


// Update assignment status based on completed trips
async function updateAssignmentStatus(client, assignmentId) {
  // First get assignment details without locking
  const assignmentResult = await client.query(`
    SELECT expected_loads_per_day FROM assignments WHERE id = $1
  `, [assignmentId]);
  
  if (assignmentResult.rows.length === 0) return;
  
  const { expected_loads_per_day } = assignmentResult.rows[0];
  
  // Then count completed trips separately
  const tripCountResult = await client.query(`
    SELECT COUNT(*) as completed_trips
    FROM trip_logs 
    WHERE assignment_id = $1 
      AND status = 'trip_completed'
      AND DATE(created_at) = CURRENT_DATE
  `, [assignmentId]);

  const completed_trips = parseInt(tripCountResult.rows[0].completed_trips);
  
  if (completed_trips >= expected_loads_per_day) {
    await client.query(`
      UPDATE assignments 
      SET status = 'completed', end_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
    `, [assignmentId]);
  }
}

// SIMPLE: Generate globally unique trip numbers using MAX(id) + 1
// This ensures no duplicate trip numbers across the entire system
async function getNextTripNumber(client, assignmentId) {
  // Get the next available trip number globally (not per assignment)
  const result = await client.query(`
    SELECT COALESCE(MAX(trip_number), 0) + 1 as next_number
    FROM trip_logs
  `);
  return result.rows[0].next_number;
}

// CRITICAL FIX: Update existing trip for new assignment instead of creating duplicate
async function updateExistingTripForNewAssignment(client, existingTrip, newAssignment, location, truck, userId, now) {
  logDebug('TRIP_UPDATE', 'Updating existing trip for new assignment', {
    existing_trip_id: existingTrip.id,
    old_assignment_id: existingTrip.assignment_id,
    new_assignment_id: newAssignment.id,
    location_name: location.name,
    location_type: location.type,
    truck_number: truck.truck_number
  });

  // Determine the appropriate update based on location type and current trip status
  let updateFields = {};
  let message = '';
  let nextStep = '';

  // Check if this is a dynamic assignment
  let isDynamicAssignment = false;
  try {
    const assignmentNotes = JSON.parse(newAssignment.notes || '{}');
    isDynamicAssignment = assignmentNotes.creation_method === 'dynamic_assignment';
  } catch (error) {
    isDynamicAssignment = false;
  }

  if (location.type === 'loading') {
    // Update loading location information
    updateFields.actual_loading_location_id = location.id;

    // If trip hasn't started loading yet, start it now
    if (!existingTrip.loading_start_time) {
      updateFields.loading_start_time = now;
      updateFields.status = 'loading_start';
      message = `Loading started at ${location.name} (updated assignment)`;
      nextStep = 'scan_loading_end';
    } else if (existingTrip.status === 'loading_start' && parseInt(location.id) === parseInt(existingTrip.actual_loading_location_id)) {
      // CRITICAL FIX: Progress trip from loading_start to loading_end
      const loadingDuration = Math.round(
        (now - new Date(existingTrip.loading_start_time)) / (1000 * 60)
      );
      updateFields.status = 'loading_end';
      updateFields.loading_end_time = now;
      updateFields.loading_duration_minutes = loadingDuration;
      message = `Loading completed in ${loadingDuration} minutes. Proceed to unloading location.`;
      nextStep = 'travel_to_unloading';

      // Debug logging to confirm this path is taken
      logDebug('LOADING_PROGRESSION_SUCCESS', 'Trip progressed from loading_start to loading_end', {
        trip_id: existingTrip.id,
        location_id: location.id,
        actual_loading_location_id: existingTrip.actual_loading_location_id,
        loading_duration_minutes: loadingDuration,
        message: message
      });
    } else {
      // Debug logging to understand why progression didn't happen
      logDebug('LOADING_PROGRESSION_FAILED', 'Trip progression conditions not met', {
        trip_id: existingTrip.id,
        trip_status: existingTrip.status,
        location_id: location.id,
        location_id_type: typeof location.id,
        actual_loading_location_id: existingTrip.actual_loading_location_id,
        actual_loading_location_id_type: typeof existingTrip.actual_loading_location_id,
        status_match: existingTrip.status === 'loading_start',
        location_match: parseInt(location.id) === parseInt(existingTrip.actual_loading_location_id)
      });

      message = `Loading location updated to ${location.name}`;
      nextStep = 'continue_current_phase';
    }
  } else if (location.type === 'unloading') {
    // Update unloading location information
    updateFields.actual_unloading_location_id = location.id;

    // If trip is ready for unloading, start it now
    if (existingTrip.loading_end_time && !existingTrip.unloading_start_time) {
      updateFields.unloading_start_time = now;
      updateFields.status = 'unloading_start';
      message = `Unloading started at ${location.name} (updated assignment)`;
      nextStep = 'scan_unloading_end';
    } else if (!existingTrip.loading_start_time && isDynamicAssignment) {
      // Dynamic route discovery: truck scanned at unloading location first
      updateFields.unloading_start_time = now;
      updateFields.status = 'unloading_start';
      message = `Dynamic route discovered: Unloading started at ${location.name}`;
      nextStep = 'scan_unloading_end';
    } else {
      message = `Unloading location updated to ${location.name}`;
      nextStep = 'continue_current_phase';
    }
  }

  // Update the assignment_id to the new assignment
  updateFields.assignment_id = newAssignment.id;
  updateFields.updated_at = now;

  // Add notes about the assignment update
  const updateNotes = {
    assignment_updated: true,
    old_assignment_id: existingTrip.assignment_id,
    new_assignment_id: newAssignment.id,
    update_location: location.name,
    update_location_id: location.id,
    update_timestamp: now.toISOString(),
    update_reason: 'Auto Create Assignment triggered location update'
  };

  // Build the UPDATE query dynamically
  const setClause = Object.keys(updateFields).map((field, index) =>
    `${field} = $${index + 2}`
  ).join(', ');

  const updateQuery = `
    UPDATE trip_logs
    SET ${setClause},
        notes = COALESCE(notes::jsonb, '{}'::jsonb) || $${Object.keys(updateFields).length + 2}::jsonb
    WHERE id = $1
    RETURNING *
  `;

  const queryParams = [
    existingTrip.id,
    ...Object.values(updateFields),
    updateNotes
  ];

  const updateResult = await client.query(updateQuery, queryParams);
  const updatedTrip = updateResult.rows[0];

  logDebug('TRIP_UPDATE_SUCCESS', 'Successfully updated existing trip', {
    trip_id: updatedTrip.id,
    new_status: updatedTrip.status,
    new_assignment_id: updatedTrip.assignment_id,
    updated_fields: Object.keys(updateFields)
  });

  // Log the scan
  await logScan(client, {
    trip_log_id: updatedTrip.id,
    scan_type: 'location',
    scanned_data: JSON.stringify({
      type: 'location',
      id: location.location_code,
      name: location.name,
      timestamp: now.toISOString()
    }),
    scanned_location_id: location.id,
    scanned_truck_id: truck.id,
    scanner_user_id: userId,
    is_valid: true,
    validation_error: null,
    ip_address: null,
    user_agent: null
  });

  return {
    success: true,
    message,
    trip: updatedTrip,
    assignment: newAssignment,
    nextStep,
    action: 'trip_updated'
  };
}

// Map API scan types to database enum values
function mapScanTypeToEnum(apiScanType) {
  const scanTypeMapping = {
    'location': 'location_scan',
    'truck': 'truck_scan'
  };
  return scanTypeMapping[apiScanType] || apiScanType;
}

// Enhanced scan logging with better error handling
async function logScan(client, scanData) {
  try {
    // Map the scan_type to the database enum value
    const dbScanType = mapScanTypeToEnum(scanData.scan_type);

    await client.query(`
      INSERT INTO scan_logs
      (trip_log_id, scan_type, scanned_data, scanned_location_id, scanned_truck_id,
       scanner_user_id, is_valid, validation_error, ip_address, user_agent, scan_timestamp)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP)
    `, [
      scanData.trip_log_id,
      dbScanType,
      scanData.scanned_data,
      scanData.scanned_location_id,
      scanData.scanned_truck_id,
      scanData.scanner_user_id,
      scanData.is_valid,
      scanData.validation_error,
      scanData.ip_address,
      scanData.user_agent
    ]);
  } catch (error) {
    logError('SCAN_LOG', error, { scanData });
  }
}

// @route   GET /api/scanner/status/:tripId
// @desc    Get current trip status with optimized query
// @access  Private
router.get('/status/:tripId', auth, async (req, res) => {
  try {
    const { tripId } = req.params;

    const result = await query(`
      SELECT 
        tl.*,
        a.loading_location_id, a.unloading_location_id,
        ll.name as loading_location_name, ll.location_code as loading_location_code,
        ul.name as unloading_location_name, ul.location_code as unloading_location_code,
        al.name as actual_loading_location_name,
        aul.name as actual_unloading_location_name,
        dt.truck_number, dt.license_plate,
        d.full_name as driver_name,
        ap.status as approval_status,
        ap.decision_reason as approval_notes
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      LEFT JOIN approvals ap ON ap.trip_log_id = tl.id
      WHERE tl.id = $1
      ORDER BY ap.created_at DESC
      LIMIT 1
    `, [tripId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Trip not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    logError('GET_TRIP_STATUS', error, { tripId });
    res.status(500).json({
      success: false,
      error: 'Failed to get trip status'
    });
  }
});

// @route   GET /api/scanner/validate-assignment/:truckId
// @desc    Validate if truck has active assignment
// @access  Private
router.get('/validate-assignment/:truckId', auth, async (req, res) => {
  try {
    const { truckId } = req.params;

    const result = await query(`
      SELECT 
        a.*,
        dt.truck_number,
        d.full_name as driver_name,
        ll.name as loading_location,
        ul.name as unloading_location
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
      ORDER BY a.created_at DESC
      LIMIT 1
    `, [truckId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No active assignment found for this truck'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    logError('VALIDATE_ASSIGNMENT', error, { truckId });
    res.status(500).json({
      success: false,
      error: 'Failed to validate assignment'
    });  }
});

// ============================================================================
// MULTI-LOCATION WORKFLOW FUNCTIONS
// ============================================================================

/**
 * Check if truck recently completed a trip (within last 30 minutes)
 * ENHANCED: Find the best baseline trip for workflow extension
 * @param {Object} client - Database client
 * @param {number} truckId - Truck ID
 * @param {Object} location - Current location object
 * @returns {Object|null} Recent completed trip suitable for workflow extension or null
 */
async function checkRecentCompletedTrip(client, truckId, location) {
  try {
    // CRITICAL FIX: Look for trips that can be extended to this location
    // Priority: Find trips where current location is different from both loading and unloading
    const result = await client.query(`
      WITH workflow_analysis AS (
        SELECT
          tl.*,
          a.loading_location_id,
          a.unloading_location_id,
          ll.name as loading_location_name,
          ul.name as unloading_location_name,
          -- Calculate workflow potential
          CASE
            WHEN $2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id)
             AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'extended'
            WHEN $2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id) THEN 'cycle'
            ELSE 'none'
          END as workflow_potential
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        WHERE a.truck_id = $1
          AND tl.status = 'trip_completed'
          AND tl.trip_completed_time >= NOW() - INTERVAL '30 minutes'
          AND (
            -- Find trips that can be extended (location is different from both loading/unloading)
            ($2 != COALESCE(tl.actual_loading_location_id, a.loading_location_id)
             AND $2 != COALESCE(tl.actual_unloading_location_id, a.unloading_location_id))
            OR
            -- Or find trips that can be cycled (location matches unloading)
            ($2 = COALESCE(tl.actual_unloading_location_id, a.unloading_location_id))
          )
      )
      SELECT * FROM workflow_analysis
      ORDER BY
        -- Prioritize extended workflows over cycles
        CASE WHEN workflow_potential = 'extended' THEN 1 ELSE 2 END,
        trip_completed_time DESC
      LIMIT 1
    `, [truckId, location.id]);

    return result.rows.length > 0 ? result.rows[0] : null;
  } catch (error) {
    logError('CHECK_RECENT_COMPLETED_TRIP', error, { truckId, locationId: location.id });
    return null;
  }
}

/**
 * Handle post-completion loading scenarios for multi-location workflows
 * @param {Object} client - Database client
 * @param {Object} completedTrip - Recently completed trip
 * @param {Object} location - Current location
 * @param {Object} truck - Truck object
 * @param {number} userId - User ID
 * @returns {Object|null} Workflow result or null
 */
async function handlePostCompletionLoading(client, completedTrip, location, truck, userId) {
  try {
    // CRITICAL BUSINESS LOGIC: Distinguish between extension and independent assignment scenarios
    // - Extension scenarios: Truck scans at unloading location (create C→B extension)
    // - Independent scenarios: Truck scans at NEW loading location (create independent C→? assignment)

    logDebug('POST_COMPLETION_ANALYSIS', 'Analyzing post-completion scenario', {
      completed_trip_id: completedTrip.id,
      previous_route: `${completedTrip.loading_location_name} → ${completedTrip.unloading_location_name}`,
      new_location: location.name,
      new_location_type: location.type,
      new_location_id: location.id
    });

    // Check if this is a new loading location (independent assignment scenario)
    if (location.type === 'loading') {
      const actualLoadingId = completedTrip.actual_loading_location_id || completedTrip.loading_location_id;
      const actualUnloadingId = completedTrip.actual_unloading_location_id || completedTrip.unloading_location_id;

      // If truck scans at a NEW loading location (different from both previous loading and unloading)
      if (location.id !== actualLoadingId && location.id !== actualUnloadingId) {
        logDebug('INDEPENDENT_ASSIGNMENT_SCENARIO', 'Creating independent assignment for new loading location', {
          completed_trip_id: completedTrip.id,
          new_loading_location: location.name,
          new_loading_location_id: location.id,
          previous_loading_id: actualLoadingId,
          previous_unloading_id: actualUnloadingId,
          scenario: 'independent_assignment'
        });

        // Create independent assignment using AutoAssignmentCreator
        const autoAssignmentCreator = new AutoAssignmentCreator();

        let autoAssignment;
        try {
          // Create independent assignment with C → placeholder route structure
          autoAssignment = await autoAssignmentCreator.createAutoAssignment({
            truck,
            location,
            client,
            userId,
            enableDynamicRouting: true, // Enable progressive route discovery for unloading location
            independentAssignment: true // Create independent assignment with placeholder unloading location
          });

          if (!autoAssignment || !autoAssignment.success) {
            throw new Error(`Auto-assignment creation failed: ${autoAssignment?.error || 'Unknown error'}`);
          }

          autoAssignment = autoAssignment.assignment; // Extract the assignment object
        } catch (autoAssignmentError) {
          logError('INDEPENDENT_ASSIGNMENT_ERROR', autoAssignmentError, {
            truck_id: truck.id,
            location_id: location.id,
            error_message: autoAssignmentError.message
          });
          throw new Error(`Failed to create independent assignment: ${autoAssignmentError.message}`);
        }

        logDebug('INDEPENDENT_ASSIGNMENT_CREATED', 'Created independent assignment for new loading location', {
          assignment_id: autoAssignment.id,
          assignment_code: autoAssignment.assignment_code,
          loading_location: location.name,
          unloading_location: 'TBD (dynamic discovery)',
          creation_method: 'independent_auto_assignment'
        });

        // Create new trip with proper independent assignment metadata
        return await handleNewTrip(
          client,
          autoAssignment,
          location,
          truck,
          userId,
          new Date()
        );
      }
    }

    // For all other scenarios, use the existing workflow logic
    const workflowType = determineWorkflowType(completedTrip, location);

    logDebug('WORKFLOW_TYPE_DETERMINATION', 'Determined workflow type for extension/cycle scenario', {
      completed_trip_id: completedTrip.id,
      previous_route: `${completedTrip.loading_location_name} → ${completedTrip.unloading_location_name}`,
      new_location: location.name,
      new_location_type: location.type,
      workflow_type: workflowType
    });

    if (workflowType === 'extended') {
      // A→B→C extension: Create new assignment for C→B route
      return await createExtendedTrip(client, completedTrip, location, truck, userId);
    } else if (workflowType === 'cycle') {
      // C→B→C cycle: Create new assignment for cycle route
      return await createCycleTrip(client, completedTrip, location, truck, userId);
    }

    return null;
  } catch (error) {
    logError('HANDLE_POST_COMPLETION_LOADING', error, {
      completedTripId: completedTrip.id,
      locationId: location.id,
      truckId: truck.id
    });
    return null;
  }
}

/**
 * Determine workflow type based on completed trip and new location
 * ENHANCED: Use actual location IDs for accurate workflow detection
 * @param {Object} completedTrip - Recently completed trip
 * @param {Object} location - Current location
 * @returns {string} Workflow type: 'extended', 'cycle', or 'none'
 */
function determineWorkflowType(completedTrip, location) {
  // Use actual location IDs if available, fallback to assignment location IDs
  const actualLoadingId = completedTrip.actual_loading_location_id || completedTrip.loading_location_id;
  const actualUnloadingId = completedTrip.actual_unloading_location_id || completedTrip.unloading_location_id;

  // If new location is different from both previous loading and unloading locations
  if (location.id !== actualLoadingId && location.id !== actualUnloadingId) {
    return 'extended'; // A→B→C extension
  }

  // If new location is the same as previous unloading location
  if (location.id === actualUnloadingId) {
    return 'cycle'; // C→B→C cycle (loading at previous unloading location)
  }

  return 'none'; // Standard workflow, no extension needed
}

/**
 * Update location sequence for workflow tracking
 * @param {Object} client - Database client
 * @param {Object} trip - Trip object
 * @param {Object} assignment - Assignment object
 * @param {Object} location - Current location
 * @param {string} status - Trip status
 */
async function updateLocationSequence(client, trip, assignment, location, status) {
  try {
    // Build location sequence based on trip progression
    const locationSequence = [];

    // Add loading location
    if (assignment.loading_location_id) {
      locationSequence.push({
        name: assignment.loading_location_name || assignment.loading_location || 'TBD Loading Location',
        type: 'loading',
        confirmed: !!trip.loading_start_time,
        location_id: assignment.loading_location_id
      });
    }

    // Add unloading location
    if (assignment.unloading_location_id) {
      locationSequence.push({
        name: assignment.unloading_location_name || assignment.unloading_location || 'TBD Unloading Location',
        type: 'unloading',
        confirmed: !!trip.unloading_start_time,
        location_id: assignment.unloading_location_id
      });
    }

    await client.query(`
      UPDATE trip_logs
      SET location_sequence = $1,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [JSON.stringify(locationSequence), trip.id]);

  } catch (error) {
    logError('UPDATE_LOCATION_SEQUENCE', error, { tripId: trip.id });
  }
}

/**
 * Create extended trip (A→B→C workflow)
 * @param {Object} client - Database client
 * @param {Object} completedTrip - Recently completed A→B trip
 * @param {Object} location - New loading location C
 * @param {Object} truck - Truck object
 * @param {number} userId - User ID
 * @returns {Object} Extended trip result
 */
async function createExtendedTrip(client, completedTrip, location, truck, userId) {
  try {
    // Use AutoAssignmentCreator
    const creator = new AutoAssignmentCreator();

    // Create assignment for C→B route (new loading location to previous unloading location)
    const assignment = await creator.createAutoAssignment({
      truck,
      location,
      client,
      userId,
      enableDynamicRouting: false // Use standard assignment for extensions
    });

    // Create new trip linked to baseline
    const tripNumber = await getNextTripNumber(client, assignment.id);
    const now = new Date();

    // Capture active driver for the new trip
    const activeDriver = await captureActiveDriverInfo(client, truck.id, now);

    const newTripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, loading_start_time,
        actual_loading_location_id, is_extended_trip, workflow_type,
        baseline_trip_id, cycle_number,
        performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
        performed_by_shift_id, performed_by_shift_type,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `, [
      assignment.id,
      tripNumber,
      'loading_start',
      now,
      location.id,
      true,
      'extended',
      completedTrip.id,
      1,
      activeDriver?.driver_id || null,
      activeDriver?.driver_name || null,
      activeDriver?.employee_id || null,
      activeDriver?.shift_id || null,
      activeDriver?.shift_type || null,
      now,
      now
    ]);

    const newTrip = newTripResult.rows[0];

    // CRITICAL: Mark baseline trip as extended in notes (preserve status = 'trip_completed')
    await client.query(`
      UPDATE trip_logs
      SET notes = COALESCE(notes::jsonb, '{}'::jsonb) || $1::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [
      JSON.stringify({
        extended_workflow: true,
        extended_at: new Date().toISOString(),
        extended_trip_id: newTrip.id,
        workflow_continuation: 'extended_to_next_location'
      }),
      completedTrip.id
    ]);

    logDebug('BASELINE_TRIP_MARKED_EXTENDED', 'Marked baseline trip as extended', {
      baseline_trip_id: completedTrip.id,
      status_preserved: 'trip_completed',
      extended_trip_id: newTrip.id,
      reason: 'extended_workflow_started'
    });

    // Update location sequence for extended workflow
    await updateLocationSequence(client, newTrip, assignment, location, 'loading_start');

    logDebug('EXTENDED_TRIP_CREATED', 'Created extended trip successfully', {
      baseline_trip_id: completedTrip.id,
      new_trip_id: newTrip.id,
      assignment_id: assignment.id,
      workflow_type: 'extended',
      route: `${completedTrip.loading_location_name} → ${completedTrip.unloading_location_name} → ${location.name}`
    });

    // Send WebSocket notification for extended trip
    try {
      const { notifyTripExtended } = require('../websocket');
      notifyTripExtended({
        id: newTrip.id,
        truck_number: truck.truck_number,
        baseline_trip_id: completedTrip.id
      }, location);
    } catch (notifyError) {
      console.error('Failed to send extended trip notification:', notifyError);
    }

    return {
      success: true,
      message: `Extended trip started! Loading at ${location.name}`,
      trip: newTrip,
      assignment: assignment,
      nextStep: 'scan_loading_end',
      action: 'extended_trip_created',
      workflow_type: 'extended'
    };

  } catch (error) {
    logError('CREATE_EXTENDED_TRIP', error, {
      completedTripId: completedTrip.id,
      locationId: location.id,
      truckId: truck.id
    });
    return null;
  }
}

/**
 * Create cycle trip (C→B→C workflow)
 * @param {Object} client - Database client
 * @param {Object} completedTrip - Recently completed trip
 * @param {Object} location - Cycle loading location
 * @param {Object} truck - Truck object
 * @param {number} userId - User ID
 * @returns {Object} Cycle trip result
 */
async function createCycleTrip(client, completedTrip, location, truck, userId) {
  try {
    // Use AutoAssignmentCreator
    const creator = new AutoAssignmentCreator();

    // Create assignment for C→B→C cycle
    const assignment = await creator.createAutoAssignment({
      truck,
      location,
      client,
      userId,
      enableDynamicRouting: false // Use standard assignment for cycles
    });

    // Determine cycle number
    const cycleCountResult = await client.query(`
      SELECT COALESCE(MAX(cycle_number), 0) + 1 as next_cycle
      FROM trip_logs
      WHERE baseline_trip_id = $1 OR id = $1
    `, [completedTrip.baseline_trip_id || completedTrip.id]);

    const cycleNumber = cycleCountResult.rows[0].next_cycle;
    const tripNumber = await getNextTripNumber(client, assignment.id);
    const now = new Date();

    // Capture active driver for the new trip
    const activeDriver = await captureActiveDriverInfo(client, truck.id, now);

    const newTripResult = await client.query(`
      INSERT INTO trip_logs (
        assignment_id, trip_number, status, loading_start_time,
        actual_loading_location_id, is_extended_trip, workflow_type,
        baseline_trip_id, cycle_number,
        performed_by_driver_id, performed_by_driver_name, performed_by_employee_id,
        performed_by_shift_id, performed_by_shift_type,
        created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      RETURNING *
    `, [
      assignment.id,
      tripNumber,
      'loading_start',
      now,
      location.id,
      true,
      'cycle',
      completedTrip.baseline_trip_id || completedTrip.id,
      cycleNumber,
      activeDriver?.driver_id || null,
      activeDriver?.driver_name || null,
      activeDriver?.employee_id || null,
      activeDriver?.shift_id || null,
      activeDriver?.shift_type || null,
      now,
      now
    ]);

    const newTrip = newTripResult.rows[0];

    // CRITICAL: Mark previous trip as continued in notes (preserve status = 'trip_completed')
    const baselineTripId = completedTrip.baseline_trip_id || completedTrip.id;
    await client.query(`
      UPDATE trip_logs
      SET notes = COALESCE(notes::jsonb, '{}'::jsonb) || $1::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [
      JSON.stringify({
        cycle_workflow: true,
        cycle_continued_at: new Date().toISOString(),
        next_cycle_trip_id: newTrip.id,
        cycle_number: cycleNumber,
        workflow_continuation: 'continued_as_cycle'
      }),
      completedTrip.id
    ]);

    logDebug('PREVIOUS_TRIP_MARKED_CONTINUED', 'Marked previous trip as continued', {
      previous_trip_id: completedTrip.id,
      baseline_trip_id: baselineTripId,
      status_preserved: 'trip_completed',
      next_cycle_trip_id: newTrip.id,
      reason: 'cycle_workflow_continued'
    });

    // Update location sequence for cycle workflow
    await updateLocationSequence(client, newTrip, assignment, location, 'loading_start');

    logDebug('CYCLE_TRIP_CREATED', 'Created cycle trip successfully', {
      baseline_trip_id: completedTrip.baseline_trip_id || completedTrip.id,
      new_trip_id: newTrip.id,
      assignment_id: assignment.id,
      workflow_type: 'cycle',
      cycle_number: cycleNumber,
      route: `${location.name} → ${completedTrip.unloading_location_name} → ${location.name}`
    });

    // Send WebSocket notification for cycle trip
    try {
      const { notifyCycleStarted } = require('../websocket');
      notifyCycleStarted({
        id: newTrip.id,
        truck_number: truck.truck_number,
        baseline_trip_id: completedTrip.baseline_trip_id || completedTrip.id
      }, cycleNumber);
    } catch (notifyError) {
      console.error('Failed to send cycle trip notification:', notifyError);
    }

    return {
      success: true,
      message: `Cycle trip #${cycleNumber} started! Loading at ${location.name}`,
      trip: newTrip,
      assignment: assignment,
      nextStep: 'scan_loading_end',
      action: 'cycle_trip_created',
      workflow_type: 'cycle',
      cycle_number: cycleNumber
    };

  } catch (error) {
    logError('CREATE_CYCLE_TRIP', error, {
      completedTripId: completedTrip.id,
      locationId: location.id,
      truckId: truck.id
    });
    return null;
  }
}

// Export both the router and the processTruckScan function for testing
module.exports = router;
module.exports.processTruckScan = processTruckScan;