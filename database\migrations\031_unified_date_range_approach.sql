-- ============================================================================
-- Migration: Unified Date Range Approach for Shifts
-- Purpose: Simplify shift system to always use start_date/end_date
-- Date: 2025-07-11
-- ============================================================================

-- Update the constraint to support unified approach
-- Remove the old constraint that required shift_date for single patterns
ALTER TABLE driver_shifts DROP CONSTRAINT IF EXISTS valid_shift_date_config;

-- Add new unified constraint: always require start_date and end_date
ALTER TABLE driver_shifts
ADD CONSTRAINT unified_shift_date_config CHECK (
    start_date IS NOT NULL AND 
    end_date IS NOT NULL AND 
    start_date <= end_date
);

-- Update existing single-day shifts to use unified approach
-- Set start_date and end_date from shift_date for any remaining legacy shifts
UPDATE driver_shifts
SET
    start_date = COALESCE(start_date, shift_date),
    end_date = COALESCE(end_date, shift_date),
    recurrence_pattern = CASE
        WHEN COALESCE(start_date, shift_date) = COALESCE(end_date, shift_date) THEN 'single'::recurrence_pattern
        ELSE 'custom'::recurrence_pattern
    END
WHERE start_date IS NULL OR end_date IS NULL;

-- Add comment explaining the unified approach
COMMENT ON COLUMN driver_shifts.shift_date IS 
'Legacy field - NULL for unified approach. Use start_date/end_date for all shifts.';

COMMENT ON COLUMN driver_shifts.start_date IS 
'Start date of shift range. For single-day shifts, same as end_date.';

COMMENT ON COLUMN driver_shifts.end_date IS 
'End date of shift range. For single-day shifts, same as start_date.';

-- Create index for efficient unified date range queries
CREATE INDEX IF NOT EXISTS idx_driver_shifts_unified_range 
ON driver_shifts (truck_id, driver_id, start_date, end_date, status);

-- Update the shift display function to handle unified approach
CREATE OR REPLACE FUNCTION get_shift_display_date(
    p_shift_date DATE,
    p_start_date DATE,
    p_end_date DATE,
    p_recurrence_pattern TEXT
) RETURNS TEXT AS $$
BEGIN
    -- Unified approach: always use start_date/end_date
    IF p_start_date = p_end_date THEN
        -- Single day shift
        RETURN p_start_date::TEXT;
    ELSE
        -- Multi-day shift
        RETURN p_start_date::TEXT || ' to ' || p_end_date::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Test the unified approach
DO $$
DECLARE
    test_count INTEGER;
    invalid_count INTEGER;
BEGIN
    -- Count total shifts
    SELECT COUNT(*) INTO test_count FROM driver_shifts;
    
    -- Count shifts that don't meet unified requirements
    SELECT COUNT(*) INTO invalid_count 
    FROM driver_shifts 
    WHERE start_date IS NULL OR end_date IS NULL OR start_date > end_date;
    
    RAISE NOTICE 'Unified approach validation:';
    RAISE NOTICE '- Total shifts: %', test_count;
    RAISE NOTICE '- Invalid shifts: %', invalid_count;
    
    IF invalid_count = 0 THEN
        RAISE NOTICE '✅ All shifts comply with unified date range approach';
    ELSE
        RAISE NOTICE '⚠️ % shifts need manual review', invalid_count;
    END IF;
END $$;

-- Success message
DO $$ 
BEGIN 
    RAISE NOTICE 'Migration 031 completed successfully: Unified date range approach implemented';
    RAISE NOTICE '- Removed old shift_date constraint';
    RAISE NOTICE '- Added unified start_date/end_date constraint';
    RAISE NOTICE '- Updated existing shifts to unified format';
    RAISE NOTICE '- Single day: start_date = end_date';
    RAISE NOTICE '- Multi day: start_date ≠ end_date';
    RAISE NOTICE '- All shifts now use consistent start_date/end_date approach';
END $$;
